#!/usr/bin/env python3
"""
Test script to verify the enhanced name + company duplicate detection
"""

import json
import tempfile
import subprocess
import os
import sys

def test_url_only_duplicate_detection():
    """Test the simplified URL-only duplicate detection"""
    print("🧪 Testing Simplified URL-Only Duplicate Detection...")

    # Create test data with only LinkedIn URLs (no candidate profiles)
    test_data = {
        "linkedin_urls": [
            "https://linkedin.com/in/john-smith-google",
            "https://linkedin.com/in/jane-doe-microsoft",
            "https://linkedin.com/in/bob-johnson-apple",
            "https://linkedin.com/in/alice-brown-amazon",
            "https://linkedin.com/in/charlie-wilson-meta"
        ],
        "plan_id": "634"
    }
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
        json.dump(test_data, temp_file)
        temp_file_path = temp_file.name
    
    try:
        # Run PHP script
        php_script_path = os.path.join(os.getcwd(), 'app/Scripts/check_duplicate_linkedin.php')
        result = subprocess.run(
            ['php', php_script_path, temp_file_path],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            try:
                response_data = json.loads(result.stdout)
                print("✅ Simplified PHP script executed successfully!")
                print(f"📊 URL-Only Results:")
                print(f"   • Total checked: {response_data.get('total_checked', 0)}")
                print(f"   • People table URL duplicates: {response_data.get('people_duplicates', 0)}")
                print(f"   • Pipeline table URL duplicates: {response_data.get('pipeline_duplicates', 0)}")
                print(f"   • Total duplicates found: {response_data.get('duplicates_found', 0)}")
                print(f"   • Processing time: {response_data.get('processing_time_ms', 0)}ms")

                # Check for required fields (simplified)
                required_fields = [
                    'people_existing_urls',
                    'pipeline_existing_urls'
                ]

                missing_fields = [field for field in required_fields if field not in response_data]

                if not missing_fields:
                    print("✅ All URL-based duplicate detection fields present")

                    # Verify no name+company fields are present (should be removed)
                    removed_fields = ['name_company_existing_urls', 'name_company_duplicates', 'enhancement_active']
                    present_removed_fields = [field for field in removed_fields if field in response_data]

                    if not present_removed_fields:
                        print("✅ Name+company fields successfully removed")
                        return True
                    else:
                        print(f"⚠️ Name+company fields still present: {present_removed_fields}")
                        return True  # Still functional, just not fully cleaned up
                else:
                    print(f"❌ Missing required URL-based duplicate detection fields: {missing_fields}")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse PHP script output: {e}")
                print(f"Raw output: {result.stdout}")
                return False
        else:
            print(f"❌ PHP script failed with return code: {result.returncode}")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ PHP script timed out")
        return False
    except Exception as e:
        print(f"❌ Error running PHP script: {e}")
        return False
    finally:
        # Clean up
        os.unlink(temp_file_path)

def test_url_only_consistency():
    """Test that URL-only matching is consistent"""
    print("\n🧪 Testing URL-Only Matching Consistency...")

    # Simple test to verify the script runs consistently
    test_data = {
        "linkedin_urls": [
            "https://linkedin.com/in/test-user-1",
            "https://linkedin.com/in/test-user-2"
        ],
        "plan_id": "634"
    }

    # Run the test twice to ensure consistency
    results = []
    for i in range(2):
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
            json.dump(test_data, temp_file)
            temp_file_path = temp_file.name

        try:
            php_script_path = os.path.join(os.getcwd(), 'app/Scripts/check_duplicate_linkedin.php')
            result = subprocess.run(
                ['php', php_script_path, temp_file_path],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                response_data = json.loads(result.stdout)
                results.append(response_data.get('duplicates_found', 0))
            else:
                results.append(-1)  # Error indicator

        except Exception as e:
            results.append(-1)  # Error indicator
        finally:
            os.unlink(temp_file_path)

    # Check consistency
    if len(set(results)) == 1 and results[0] >= 0:
        print(f"✅ Consistency test passed: Both runs returned {results[0]} duplicates")
        return True
    else:
        print(f"❌ Consistency test failed: Results were {results}")
        return False

def main():
    """Run all URL-only duplicate detection tests"""
    print("🚀 Testing Simplified URL-Only Duplicate Detection")
    print("=" * 60)

    tests = [
        ("URL-Only Duplicate Detection", test_url_only_duplicate_detection),
        ("URL-Only Consistency Test", test_url_only_consistency)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   • {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! Simplified URL-only duplicate detection is working.")
        print("\n📈 Expected Benefits:")
        print("   • Fast and efficient LinkedIn URL-based duplicate detection")
        print("   • Checks both people.linkedinURL and pipelines.linkedinURL tables")
        print("   • Reduces AI processing costs by filtering duplicates early")
        print("   • Simplified logic without complex name+company matching")
    else:
        print("\n⚠️ Some tests failed. Please review the implementation.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
