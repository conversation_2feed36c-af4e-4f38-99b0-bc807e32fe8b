#!/usr/bin/env python3
"""
Test script to verify that early duplicate detection is working in the external search flow

This script creates a minimal test plan and runs the external search to verify that:
1. Early duplicate detection logs appear
2. The duplicate detection is executed before AI processing
3. The performance metrics include duplicate detection savings

Usage: python scripts/test_external_search_flow.py
"""

import json
import tempfile
import os
import sys

def create_test_plan():
    """Create a minimal test plan for external search"""
    return {
        "plan_name": "Test Early Duplicate Detection",
        "plan_id": "test-duplicate-detection-001",
        "target_roles": ["Software Engineer", "Data Scientist"],
        "companies": ["Google", "Microsoft"],
        "gender": "Not required",
        "country": ["United States"],
        "minimum_tenure": 2,
        "include_alumni": True,
        "enable_early_duplicate_detection": True,
        "skills": ["Python", "Machine Learning"],
        "qualifications": ["Bachelor's Degree"],
        "step_up_candidates": ["Junior Developer"],
        "alternative_roles_titles": ["ML Engineer"]
    }

def test_external_search_with_duplicate_detection():
    """Test the external search with early duplicate detection enabled"""
    print("🧪 Testing External Search with Early Duplicate Detection")
    print("=" * 60)
    
    # Create test plan
    test_plan = create_test_plan()
    print(f"📋 Test Plan: {test_plan['plan_name']}")
    print(f"🎯 Target Roles: {', '.join(test_plan['target_roles'])}")
    print(f"🏢 Companies: {', '.join(test_plan['companies'])}")
    print(f"🔄 Early Duplicate Detection: {test_plan['enable_early_duplicate_detection']}")
    
    # Create temporary files
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as input_file:
        json.dump(test_plan, input_file, indent=2)
        input_file_path = input_file.name
    
    output_file_path = tempfile.mktemp(suffix='.json')
    
    try:
        # Get script path
        script_dir = os.path.dirname(os.path.abspath(__file__))
        python_script_path = os.path.join(script_dir, 'python', 'exa_search_standalone.py')
        
        print(f"📁 Python script path: {python_script_path}")
        print(f"📄 Input file: {input_file_path}")
        print(f"📄 Output file: {output_file_path}")
        
        # Build command
        command = [
            'python', python_script_path,
            '--plan-file', input_file_path,
            '--output', output_file_path,
            '--num-results', '10'  # Small number for testing
        ]
        
        print(f"🚀 Executing command: {' '.join(command)}")
        print("\n" + "=" * 60)
        print("📊 SEARCH OUTPUT:")
        print("=" * 60)
        
        # Execute the command and capture output
        import subprocess
        result = subprocess.run(command, capture_output=True, text=True, timeout=300)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("\nSTDERR:")
            print(result.stderr)
        
        print("\n" + "=" * 60)
        print(f"📤 Return code: {result.returncode}")
        
        # Check for expected log messages
        output = result.stdout + result.stderr
        
        # Check for early duplicate detection logs
        duplicate_detection_logs = [
            "Starting early duplicate detection",
            "EARLY DUPLICATE DETECTION:",
            "COST SAVINGS:",
            "Processing efficiency gain"
        ]
        
        found_logs = []
        for log_msg in duplicate_detection_logs:
            if log_msg in output:
                found_logs.append(log_msg)
        
        print("\n📋 Log Analysis:")
        print(f"   • Expected duplicate detection logs: {len(duplicate_detection_logs)}")
        print(f"   • Found duplicate detection logs: {len(found_logs)}")
        
        for log_msg in found_logs:
            print(f"   ✅ Found: '{log_msg}'")
        
        for log_msg in duplicate_detection_logs:
            if log_msg not in found_logs:
                print(f"   ❌ Missing: '{log_msg}'")
        
        # Check if output file was created
        if os.path.exists(output_file_path):
            print(f"   ✅ Output file created: {output_file_path}")
            
            # Try to read and parse the output
            try:
                with open(output_file_path, 'r') as f:
                    output_data = json.load(f)
                
                if 'profiles' in output_data:
                    print(f"   ✅ Found {len(output_data['profiles'])} profiles in output")
                else:
                    print(f"   ⚠️ No 'profiles' key in output data")
                    
            except Exception as e:
                print(f"   ❌ Error reading output file: {e}")
        else:
            print(f"   ❌ Output file not created")
        
        # Overall assessment
        if len(found_logs) >= 2:  # At least 2 duplicate detection logs found
            print("\n🎉 SUCCESS: Early duplicate detection appears to be working!")
            return True
        else:
            print("\n❌ FAILURE: Early duplicate detection logs not found")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Test timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"❌ Error running test: {e}")
        return False
    finally:
        # Clean up temp files
        try:
            os.unlink(input_file_path)
        except:
            pass
        try:
            os.unlink(output_file_path)
        except:
            pass

def main():
    """Main test function"""
    print("🚀 External Search Flow Test")
    print("Testing early duplicate detection integration")
    print("=" * 60)
    
    success = test_external_search_with_duplicate_detection()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ TEST PASSED: Early duplicate detection is working correctly!")
        return 0
    else:
        print("❌ TEST FAILED: Early duplicate detection is not working as expected")
        return 1

if __name__ == "__main__":
    sys.exit(main())
