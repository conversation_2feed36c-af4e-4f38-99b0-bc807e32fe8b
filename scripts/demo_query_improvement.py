#!/usr/bin/env python3
"""
Demonstration script showing the before/after improvement in search query generation

This script shows how the query generation has been improved to fix logical grouping issues.
"""

import sys
import os

# Add the scripts/python directory to the path so we can import the module
script_dir = os.path.dirname(os.path.abspath(__file__))
python_dir = os.path.join(script_dir, 'python')
sys.path.insert(0, python_dir)

# Import the ExternalSearchEngine class
from exa_search_standalone import ExternalSearchEngine

def demonstrate_query_improvement():
    """Demonstrate the before/after improvement in query generation"""
    
    print("🔍 Search Query Generation Improvement Demonstration")
    print("=" * 70)
    
    # Example plan data that would have caused issues before
    plan_data = {
        'target_roles': ['Chief Risk Officer', 'CRO', 'Risk Director'],
        'companies': ['HSBC', 'Barclays'],
        'country': ['United Kingdom', 'United States']
    }
    
    print("📋 Plan Data:")
    print(f"   • Target Roles: {plan_data['target_roles']}")
    print(f"   • Companies: {plan_data['companies']}")
    print(f"   • Countries: {plan_data['country']}")
    
    print("\n🔧 Query Generation:")
    
    # Show what the old format would have looked like (problematic)
    old_format = '"Chief Risk Officer" OR "CRO" OR "Risk Director" AND ("HSBC" OR "Barclays") AND ("United Kingdom" OR "United States")'
    print(f"\n❌ OLD FORMAT (problematic):")
    print(f"   {old_format}")
    print("   ⚠️  Issues:")
    print("   • Missing parentheses around role alternatives")
    print("   • Ambiguous precedence: could be interpreted as:")
    print('     "Chief Risk Officer" OR ("CRO" OR "Risk Director" AND companies AND countries)')
    print("   • Search engine might not find profiles matching all criteria correctly")
    
    # Generate the new improved format
    search_engine = ExternalSearchEngine()
    new_format = search_engine._build_search_query(plan_data)
    print(f"\n✅ NEW FORMAT (improved):")
    print(f"   {new_format}")
    print("   ✨ Improvements:")
    print("   • Proper parentheses around role alternatives")
    print("   • Clear logical grouping: (roles) AND (companies) AND (countries)")
    print("   • Unambiguous search logic")
    print("   • Better search precision and relevance")
    
    print("\n🎯 Expected Search Behavior:")
    print("   The search will now correctly find LinkedIn profiles that match:")
    print("   • ANY of the target roles (Chief Risk Officer OR CRO OR Risk Director)")
    print("   • AND ANY of the target companies (HSBC OR Barclays)")
    print("   • AND ANY of the target countries (United Kingdom OR United States)")
    
    print("\n📈 Benefits:")
    print("   • More relevant search results")
    print("   • Reduced false positives")
    print("   • Better candidate matching")
    print("   • Improved search engine interpretation")
    
    return True

def show_additional_examples():
    """Show additional examples of the improved query format"""
    
    print("\n" + "=" * 70)
    print("📚 Additional Examples")
    print("=" * 70)
    
    search_engine = ExternalSearchEngine()
    
    examples = [
        {
            'name': 'Single Role with Multiple Companies',
            'plan_data': {
                'target_roles': ['Software Engineer'],
                'companies': ['Google', 'Microsoft', 'Apple'],
                'country': []
            }
        },
        {
            'name': 'Multiple Roles, No Company Filter',
            'plan_data': {
                'target_roles': ['Data Scientist', 'ML Engineer', 'AI Researcher'],
                'companies': [],
                'country': ['Canada']
            }
        },
        {
            'name': 'Complex Multi-Role Search',
            'plan_data': {
                'target_roles': ['VP Engineering', 'Engineering Director', 'Head of Engineering'],
                'companies': ['Netflix', 'Spotify', 'Uber'],
                'country': ['United States', 'Canada', 'United Kingdom']
            }
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n🔍 Example {i}: {example['name']}")
        query = search_engine._build_search_query(example['plan_data'])
        print(f"   Query: {query}")
        
        # Explain the logic
        roles = example['plan_data']['target_roles']
        companies = example['plan_data']['companies']
        countries = example['plan_data']['country']
        
        logic_parts = []
        if roles:
            if len(roles) == 1:
                logic_parts.append(f'Role: "{roles[0]}"')
            else:
                role_list = " OR ".join([f'"{r}"' for r in roles])
                logic_parts.append(f'Any role: {role_list}')

        if companies:
            if len(companies) == 1:
                logic_parts.append(f'Company: "{companies[0]}"')
            else:
                company_list = " OR ".join([f'"{c}"' for c in companies])
                logic_parts.append(f'Any company: {company_list}')

        if countries:
            if len(countries) == 1:
                logic_parts.append(f'Country: "{countries[0]}"')
            else:
                country_list = " OR ".join([f'"{c}"' for c in countries])
                logic_parts.append(f'Any country: {country_list}')
        
        print(f"   Logic: {' AND '.join(logic_parts)}")

def main():
    """Run the demonstration"""
    try:
        demonstrate_query_improvement()
        show_additional_examples()
        
        print("\n" + "=" * 70)
        print("🎉 Query Generation Improvement Complete!")
        print("   The external search functionality now generates properly structured")
        print("   queries with correct logical grouping and parentheses placement.")
        print("=" * 70)
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Error during demonstration: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
