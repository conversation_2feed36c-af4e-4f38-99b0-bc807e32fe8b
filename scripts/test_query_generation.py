#!/usr/bin/env python3
"""
Test script to verify the improved search query generation in exa_search_standalone.py

This script tests the _build_search_query method to ensure:
1. Role alternatives are properly wrapped in parentheses
2. Query structure follows the expected format: ("Role1" OR "Role2") AND ("Company") AND ("Country")
3. Different combinations of roles, companies, and countries work correctly

Usage: python scripts/test_query_generation.py
"""

import sys
import os

# Add the scripts/python directory to the path so we can import the module
script_dir = os.path.dirname(os.path.abspath(__file__))
python_dir = os.path.join(script_dir, 'python')
sys.path.insert(0, python_dir)

# Import the ExternalSearchEngine class
from exa_search_standalone import ExternalSearchEngine

def test_single_role_single_company_single_country():
    """Test query generation with single role, company, and country"""
    print("🧪 Test 1: Single role, company, and country")
    
    search_engine = ExternalSearchEngine()
    plan_data = {
        'target_roles': ['Chief Risk Officer'],
        'companies': ['HSBC'],
        'country': ['United Kingdom']
    }
    
    query = search_engine._build_search_query(plan_data)
    expected = '("Chief Risk Officer") AND ("HSBC") AND ("United Kingdom")'
    
    print(f"   Generated: {query}")
    print(f"   Expected:  {expected}")
    
    if query == expected:
        print("   ✅ PASSED")
        return True
    else:
        print("   ❌ FAILED")
        return False

def test_multiple_roles_single_company_single_country():
    """Test query generation with multiple roles (the main issue we're fixing)"""
    print("\n🧪 Test 2: Multiple roles, single company, single country")
    
    search_engine = ExternalSearchEngine()
    plan_data = {
        'target_roles': ['Chief Risk Officer', 'CRO'],
        'companies': ['HSBC'],
        'country': ['United Kingdom']
    }
    
    query = search_engine._build_search_query(plan_data)
    expected = '("Chief Risk Officer" OR "CRO") AND ("HSBC") AND ("United Kingdom")'
    
    print(f"   Generated: {query}")
    print(f"   Expected:  {expected}")
    
    if query == expected:
        print("   ✅ PASSED")
        return True
    else:
        print("   ❌ FAILED")
        return False

def test_multiple_roles_multiple_companies_multiple_countries():
    """Test query generation with multiple roles, companies, and countries"""
    print("\n🧪 Test 3: Multiple roles, companies, and countries")
    
    search_engine = ExternalSearchEngine()
    plan_data = {
        'target_roles': ['Software Engineer', 'Senior Developer', 'Lead Engineer'],
        'companies': ['Google', 'Microsoft', 'Apple'],
        'country': ['United States', 'Canada']
    }
    
    query = search_engine._build_search_query(plan_data)
    expected = '("Software Engineer" OR "Senior Developer" OR "Lead Engineer") AND ("Google" OR "Microsoft" OR "Apple") AND ("United States" OR "Canada")'
    
    print(f"   Generated: {query}")
    print(f"   Expected:  {expected}")
    
    if query == expected:
        print("   ✅ PASSED")
        return True
    else:
        print("   ❌ FAILED")
        return False

def test_roles_only():
    """Test query generation with only roles (no companies or countries)"""
    print("\n🧪 Test 4: Roles only (no companies or countries)")
    
    search_engine = ExternalSearchEngine()
    plan_data = {
        'target_roles': ['Data Scientist', 'ML Engineer'],
        'companies': [],
        'country': []
    }
    
    query = search_engine._build_search_query(plan_data)
    expected = '("Data Scientist" OR "ML Engineer")'
    
    print(f"   Generated: {query}")
    print(f"   Expected:  {expected}")
    
    if query == expected:
        print("   ✅ PASSED")
        return True
    else:
        print("   ❌ FAILED")
        return False

def test_roles_and_companies_only():
    """Test query generation with roles and companies but no countries"""
    print("\n🧪 Test 5: Roles and companies only (no countries)")
    
    search_engine = ExternalSearchEngine()
    plan_data = {
        'target_roles': ['Product Manager'],
        'companies': ['Netflix', 'Spotify'],
        'country': []
    }
    
    query = search_engine._build_search_query(plan_data)
    expected = '("Product Manager") AND ("Netflix" OR "Spotify")'
    
    print(f"   Generated: {query}")
    print(f"   Expected:  {expected}")
    
    if query == expected:
        print("   ✅ PASSED")
        return True
    else:
        print("   ❌ FAILED")
        return False

def test_custom_search_query():
    """Test that custom search queries are used directly without modification"""
    print("\n🧪 Test 6: Custom search query (should be used as-is)")
    
    search_engine = ExternalSearchEngine()
    plan_data = {
        'search_query': 'Custom query without parentheses',
        'target_roles': ['Should be ignored'],
        'companies': ['Should be ignored'],
        'country': ['Should be ignored']
    }
    
    query = search_engine._build_search_query(plan_data)
    expected = 'Custom query without parentheses'
    
    print(f"   Generated: {query}")
    print(f"   Expected:  {expected}")
    
    if query == expected:
        print("   ✅ PASSED")
        return True
    else:
        print("   ❌ FAILED")
        return False

def test_none_values():
    """Test query generation with 'none' values for companies and countries"""
    print("\n🧪 Test 7: 'none' values for companies and countries")
    
    search_engine = ExternalSearchEngine()
    plan_data = {
        'target_roles': ['CEO'],
        'companies': ['none'],
        'country': ['none']
    }
    
    query = search_engine._build_search_query(plan_data)
    expected = '("CEO")'
    
    print(f"   Generated: {query}")
    print(f"   Expected:  {expected}")
    
    if query == expected:
        print("   ✅ PASSED")
        return True
    else:
        print("   ❌ FAILED")
        return False

def main():
    """Run all tests and report results"""
    print("🚀 Testing Improved Search Query Generation")
    print("=" * 60)
    
    tests = [
        test_single_role_single_company_single_country,
        test_multiple_roles_single_company_single_country,
        test_multiple_roles_multiple_companies_multiple_countries,
        test_roles_only,
        test_roles_and_companies_only,
        test_custom_search_query,
        test_none_values
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"   ❌ FAILED with exception: {str(e)}")
    
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    print(f"   • Tests passed: {passed}/{total}")
    print(f"   • Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All tests passed! Query generation is working correctly.")
        print("\n✨ Key improvements verified:")
        print("   • Role alternatives are properly wrapped in parentheses")
        print("   • Query structure follows: (\"Role1\" OR \"Role2\") AND (\"Company\") AND (\"Country\")")
        print("   • Multiple combinations work correctly")
        print("   • Custom queries are preserved as-is")
        return 0
    else:
        print(f"\n❌ {total - passed} test(s) failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
