<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Anthropic API Configuration
    |--------------------------------------------------------------------------
    |
    | This section contains the configuration for the Anthropic API,
    | including the API key, model name, and API version.
    |
    */
    'anthropic' => [
        'api_key' => env('ANTHROPIC_API_KEY'),
        'model' => env('ANTHROPIC_MODEL', 'claude-3-5-sonnet-20241022'), // Using Sonnet model for better quality responses
        'version' => env('ANTHROPIC_VERSION', '2023-06-01'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Nubela API Configuration
    |--------------------------------------------------------------------------
    |
    | This section contains the configuration for the Nubela API,
    | including the API key.
    |
    */
    'nubela' => [
        'api_key' => env('NUBELA_API_KEY'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Perplexity API Configuration
    |--------------------------------------------------------------------------
    |
    | This section contains the configuration for the Perplexity API,
    | including the API key.
    |
    */
    'perplexity' => [
        'api_key' => env('PERPLEXITY_API_KEY'),
    ],

    /*
    |--------------------------------------------------------------------------
    | AWS Bedrock Configuration
    |--------------------------------------------------------------------------
    |
    | This section contains the configuration for AWS Bedrock,
    | including access key, secret key, and region.
    |
    */
    'aws_bedrock' => [
        'access_key' => env('AWS_ACCESS_KEY_ID'),
        'secret_key' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_REGION', 'us-east-1'),
    ],

    /*
    |--------------------------------------------------------------------------
    | OpenAI API Configuration
    |--------------------------------------------------------------------------
    |
    | This section contains the configuration for the OpenAI API,
    | including the API key and organization.
    |
    */
    'openai' => [
        'api_key' => env('OPENAI_API_KEY'),
        'organization' => env('OPENAI_ORGANIZATION'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Exa AI API Configuration
    |--------------------------------------------------------------------------
    |
    | This section contains the configuration for the Exa AI API,
    | which is used for external candidate searches.
    |
    */
    'exa' => [
        'api_key' => env('EXA_API_KEY'),
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Python Integration Configuration
    |--------------------------------------------------------------------------
    |
    | This section contains the configuration for Python integration,
    | including the path to the Python binary and other settings.
    |
    */
    'python' => [
        'binary_path' => env('PYTHON_PATH', 'python'), // Path to Python executable
        'script_timeout' => env('PYTHON_SCRIPT_TIMEOUT', 1800), // Timeout in seconds (30 minutes)
        'enable_external_search' => env('PYTHON_EXTERNAL_SEARCH_ENABLED', true), // Enable/disable Python external search
    ],

    /*
    |--------------------------------------------------------------------------
    | AI Services Rate Limiting Configuration
    |--------------------------------------------------------------------------
    |
    | This section contains the rate limiting configuration for different
    | AI services to prevent API quota exhaustion and ensure fair usage.
    |
    */
    'rate_limits' => [
        'openai' => [
            'requests_per_minute' => env('OPENAI_RATE_LIMIT_RPM', 4500),
            'tokens_per_minute' => env('OPENAI_RATE_LIMIT_TPM', 750000),
        ],
        'anthropic' => [
            'requests_per_minute' => env('ANTHROPIC_RATE_LIMIT_RPM', 900),
            'tokens_per_minute' => env('ANTHROPIC_RATE_LIMIT_TPM', 80000),
            'tokens_per_minute' => env('ANTHROPIC_RATE_LIMIT_TPM', 1000000),
        ],
        'exa' => [
            'requests_per_minute' => env('EXA_RATE_LIMIT_RPM', 100),
            'searches_per_minute' => env('EXA_RATE_LIMIT_SPM', 5),
        ],
    ],

];
