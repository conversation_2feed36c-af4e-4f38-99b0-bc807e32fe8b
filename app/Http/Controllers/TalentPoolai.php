<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Models\CareerHistories;
use App\Models\notifications;
use App\Models\People;
use App\Models\Job;
use App\Models\Skills;
use App\Models\User;
use App\Models\JobPeople;
use App\Models\JobRequirement;
use App\Models\pipeline;
use App\Models\Account;
use App\Models\Company;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Jobs\TalentPoolExternalSearch;
use DateTime;


class TalentPoolai extends Controller
{
 
    private $anthropicApiKey;
    private $modelName;
    private $prompt = '';
    private $system = '';
    private $aiResponse;

    public $apiKey;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->anthropicApiKey = config('ai.anthropic.api_key');
        $this->modelName = config('ai.anthropic.model');
        $this->apiKey = config('ai.nubela.api_key');
    }

    public function index(Request $request)
    {
        // Clear the conversation from the session to reset the chat on page load
        $request->session()->forget('messages');

        return view('talentpoolai.index');
    }

    public function sendMessage(Request $request)
    {
        //dd('function starting');
        $user = auth()->user();
        $userMessage = $request->input('message');
        $planData = [];
        
        // Retrieve previous messages from the session
        $allMessages = $request->session()->get('messages', []);
        
        // Add the user's message to the full conversation
        $allMessages[] = [
            'role' => 'user',
            'content' => $userMessage
        ];
        
        // Save the complete conversation for later use
        //$request->session()->put('messages', $allMessages);
        
        // Check if we're already in aTalent Poolcreation flow
        $isCreatingPlan = $request->session()->get('creating_plan', false);

        if ($isCreatingPlan) {
            // If we're already inTalent Poolcreation mode, continue with createPlan
            Log::info("Continuing withTalent Poolcreation");
            return $this->createPlan($request);
        }
        
        // For the orchestrator, only use the last 3 messages (or fewer if there aren't 3 yet)
        $recentMessageCount = min(3, count($allMessages));
        $recentMessages = array_slice($allMessages, -$recentMessageCount);
        
        Log::info("Recent messages for orchestrator:", ['messages' => $recentMessages]);
        
        // Rest of your interestedCompanies, interestedIndustries, etc. code...

        // Prepare the payload for the Claude API with only recent messages
        $payload = [
            'model' => config('ai.anthropic.model'),
            'max_tokens' => 300,
            'system' => 'You are an orchestrator that must categorize user queries as either "Create Talent Pools" or "Research" based on strict criteria.

                        FUNCTION DEFINITIONS:
                        1- Name of function: CreatePlan
                        Description: Used only when the user explicitly wants to create a Talent Pools for specific roles.
                        Flag: Create Talent Pools
                        Example queries: "Make a Talent Pools for a CFO", "Create Talent Pools for IT leadership", "I need a Talent Pools for identifying marketing executives"

                        2- Name of function: ConductResearch
                        Description: Used for all informational queries about people, companies, or industries.
                        Flag: Research
                        Example queries: "Who is the CEO of Google?", "Tell me about leadership at Barclays", "What is the gender diversity at Amazon?", "List the executive team at HSBC", "Who works at JPMorgan?"

                        CATEGORIZATION RULES (VERY IMPORTANT):
                        - ONLY return "Create Talent Pools" when the user EXPLICITLY requests to create or make a Talent Pools, using words like "create", "make", "build", or "develop" along with "Talent Pools".
                        - If the user is asking for information about any company, person, team, or role WITHOUT explicitly requesting to create a Talent Pools, return "Research".
                        - Any query beginning with "who", "what", "where", "when", "how many", "tell me about", "list", "show me", or similar information-seeking phrases should be "Research".
                        - Questions about leadership teams, executives, company structure, or specific individuals should ALWAYS be "Research".
                        - If the query contains "leadership team", "executive committee", "executives", "exco", "board", "directors", "management team", or similar terms WITHOUT explicitly requesting to create a Talent Pools, it is "Research".
                        - When in doubt, ask the user to clarify their intent.

                        IMPORTANT: Even if there are multiple messages in the conversation history, you must only respond exactly once. Do not repeat the classification..

                        RESPONSE FORMAT:
                        Your response must only be one of these two flags or if it is not clear ask them to clarify their intent in a friendly way and nothing else:
                        1. "Create Talent Pools"
                        2. "Research"


                        In ambiguous cases where the user might be seeking information but not explicitly requesting a Talent Pools, choose "Research".',
            'messages' => $recentMessages // Include only recent messages for decision
        ];

        $response = Http::withHeaders([
            'x-api-key' => $this->anthropicApiKey,
            'anthropic-version' => config('ai.anthropic.version'),
            'Content-Type' => 'application/json',
        ])->post('https://api.anthropic.com/v1/messages', $payload);
        Log::info("Response from Anthropic");
        Log::info($response);

        $data = $response->json();
        Log::info($data);
        $aiResponse = $data['content'];
        Log::info("aiResponse");
        Log::info($aiResponse);

        Log::info("AI Response from orchestrator- 1");

        // The function to call to determine which process the user wants to use
        if($aiResponse[0]['text'] === "Create Talent Pools"){
            Log::info("Making a Talent Poolnow");
            $choice = "makePlan";
            
            // Set a session flag to indicate we're inTalent Poolcreation mode
            $request->session()->put('creating_plan', true);
            Log::info("Going to create a Talent Pool- 2");
            return $this->createPlan($request);
        }
        elseif($aiResponse[0]['text'] === "Research"){
            Log::info("Conducting Research now");
            // Make sure to clear theTalent Poolcreation flag if it was set
            $request->session()->forget('creating_plan');
            Log::info("Going to research function - 3");
            return $this->researchFunction($request);
        }
        else{
            $choice = "Undecided";
            // Keep the session state neutral
            if ($response->status() === 429) {
                Log::warning("Claude API rate limit hit", ['status' => 429]);
                // Try to queue the request instead of failing
                $payload = [
                    'model' => config('ai.anthropic.model'),
                    'max_tokens' => 300,
                    'system' => 'You are an orchestrator that must categorize user queries as either "Create Talent Pools" or "Research" based on strict criteria.',
                    'messages' => $recentMessages ?? [],
                    'queue_if_limited' => true,
                ];
                $service = new \App\Services\AI\RateLimitedAnthropicService();
                $queueResult = $service->messages($payload, null, 0);
                if (is_array($queueResult) && isset($queueResult['queued']) && $queueResult['queued']) {
                    return response()->json([
                        'queued' => true,
                        'message' => 'Your request is queued and will be processed as soon as possible.'
                    ], 202);
                }
                return response()->json([
                    'aiResponse' => $aiResponse,
                    'choice' => "makePlan",
                    'planCreated' => false,
                    'planData' => null,
                    'retry_after' => $response->header('Retry-After', 60)
                ], 429);
            }
            return response()->json([
                'aiResponse' => $aiResponse,
                'choice'     => $choice,
                'planCreated' => null,
                'planData' => null,
            ]);
        }
    }

    public function createPlan(Request $request){
        Log::info("The newTalent Poolfunction is has just been triggered");
        $choice = "makePlan";
        $user = auth()->user();
        
        $userMessage = $request->input('message'); //Getting any new message
        $planData = [];
        $messages = $request->session()->get('messages', []); //Getting the previous messages that have been recieved before this function was triggered
        // Add the user's message to the conversation
        $messages[] = [
            'role' => 'user',
            'content' => $userMessage
        ];
        
        Log::info($messages);

        $planCreated = false;

            $interestedCompanies = false;
            $interestedIndustries = false;
            $interestedSectors = false;
            $accountObj = Account::where('id', $user->account_id)->first();

            if($accountObj){
                if($accountObj->company_of_interest && $accountObj->company_of_interest != ""){
                    $interestedCompanies = explode(",", $accountObj->company_of_interest);
                    }
            else if($accountObj->industry_interest && $accountObj->industry_interest != ""){
                    $interestedIndustries = explode(",", $accountObj->industry_interest);
                            }
            else if($accountObj->sector_interest && $accountObj->sector_interest != ""){
                    $interestedSectors = explode(",", $accountObj->sector_interest);
                    }
            }

            $companyIds = [];
                if($interestedCompanies && count($interestedCompanies) > 0){
                            $companyIds = $interestedCompanies;
                }
                else if($interestedIndustries && count($interestedIndustries) > 0){
                    $companyIds = Company::whereIn('industry', $interestedIndustries)->pluck('id')->toArray();
                }
                else if($interestedSectors && count($interestedSectors) > 0){
                    $companyIds = Company::whereIn('sector', $interestedSectors)->pluck('id')->toArray();
                }

                $companies =  Company::select(['id','name','industry','sector'])
                                        ->where('id', '!=', $user->company_id)
                                                ->when(!empty($companyIds),function($query) use($companyIds){
                                                    $query->whereIn('id', $companyIds);
                                                })
                                                ->get()->map(function ($company) {
                        return [
                            'value' => $company->name,
                            'label' => $company->name,
                            'industry' => $company->industry,
                            'sector' => $company->sector
                        ];
                    })->toArray();
                $companystring = json_encode($companies);

        // Prepare the payload for the Claude API
        $payload = [
            'model' => config('ai.anthropic.model'),
            'max_tokens' => 2000,
            'system' => 'You are an assistant that helps users create Talent Pools based on their requirements provided in natural language.

                    Your main goal is to help the user create Talent Pools. Follow these instructions:

                    **General Logic:**
                    - First, analyze the users message to see if all required information is already provided. Required information includes:
                    1. A specific role (e.g., "Chief Financial Officer")
                    2. Preferred company or industry context (e.g., "any company working in the banking industry")
                    3. Whether to consider external step-up roles or not
                    4. Preferred gender for candidates

                    - If the users initial message contains all required details and does not explicitly ask for suggestions, you may directly proceed to create the Talent Pools without asking additional questions.

                    - If the users initial message does not contain all required details, or the user explicitly requests suggestions for roles and companies, proceed with the following steps.

                    **Step-by-Step Instructions:**

                            1. If the user provides a role (for example, "Chief Financial Officer"), suggest a numbered list of 10 relevant roles based on the provided role.
                            - The suggested roles must not contain abbreviations; write out the full role names.
                            - Roles should be closely related to the provided role.

                    2. Allow the user to select the desired roles by their corresponding numbers from the suggested list.

                            **Alternative Roles Titles Array:**
                            - For each chosen role, generate realistic alternative role names that might be used or called by different organizations. For example, if -Treasury Directory-, they might be called Treasury Controller or something like that. So the role might have a different name.
                            - Do not limit this to a fixed number; include multiple plausible alternatives.
                            - All these alternative roles titles for all selected roles should be collected into a single array called "alternative_roles_titles."
                            - For example, if the user selects 3 roles, and each has several alternative titles, combine them all into one single "alternative_roles_titles" array.
                            - These alternative titles are generated internally and will be included in the final data passed to the `create_plan` function, but should not be displayed or asked about in the user-facing output.

                    3. Ask the user if they would consider external step-up roles for candidates.
                    - If the user says yes, suggest a numbered list of 10 step-up roles that represent more advanced or next-level positions relative to the original provided role.
                    - The step-up roles must be full role names with no abbreviations, and must not include the names of specific individuals—only the role titles.
                    - If the user says no, set the array "step_up_candidates" as ["none"].

                            4. Ask the user if they want to look at any particular companies.
                            - If the user says yes, suggest a numbered list of 10 relevant companies from this list:'.$companystring.'.
                            - These suggested companies must not contain abbreviations; use the full company names.
                            - Allow the user to select companies by their corresponding numbers from the list.
                            - If the user says no or does not mention any company requirement explicitly, set the array "companies" as ["none"]. *** Very important!

                    5. Ask the user about their preferred gender for candidates.

                    6. Ask the user about their preferred country for candidates based on the companies chosen.
                    - If the user does not choose a country, set the array "country" as ["none"].

                    7. Once you have all required information (selected roles, chosen companies, step-up roles decision, gender), generate a Talent Pools. The Talent Pools should:
                    - Include arrays of the selected roles and companies.
                    - Reflect the chosen preferences for step-up candidates and gender.
                    - Fill in all other properties of the Talent Pools as needed based on the selected roles and companies.

                    8. After assembling all information into a Talent Pools, call the `create_talent_pool` function with the collected data.

                    **If any required information is missing, politely ask the user for that specific detail before proceeding.**

                            **Important:**
                            - If the user provided all details from their first message and did not ask for suggestions, directly create the Talent Pool without asking the questions or suggesting lists.
                            - If the user explicitly requests suggestions in their first message (e.g., "suggest relevant roles" or "suggest companies"), follow the suggestion steps even if a role or industry is already known.
                            - If the user provides a job description use the job description for a role create the Talent Pool without asking the questions or suggesting lists.
                            - If a companies is not explicitly stated, you can default to no companies (companies = ["none"]).
                            - If the countries of interest are not explicitly stated you can default to no countries (country = ["none"]).

                    **Important Example:**
                    Consider this user message:
                    "I want to make a Talent Pools for a Chief Financial Officer, willing to consider any candidates as long as they have had a relevant roles or have the right skill set. I want to look at any company that is working in the banking industry."

                    In this message:
                    - The user provided a specific role: Chief Financial Officer.
                    - The user specified the industry: banking industry.
                    - The user did not explicitly say they do or do not want step-up roles, nor did they say they do not want suggestions. They also have not specified a preferred gender.

                            -> For this scenario, because the user requirements are mostly clear and they did not explicitly request suggestions or further questions, you should:
                            - Assume they are open to any suitable target roles, so suggest the -target_roles- array based on the case.
                            - Assume they are open to any suitable candidates, so suggest the -step_up_candidates- array based on the case.
                            - If a companies is not explicitly stated, you can default to no companies (companies = ["none"]).
                            - If step-up consideration is not stated, you can default to no step-up candidates (step_up_candidates = ["none"]).
                            - If gender preference is not stated, you can default to "Not required" or another neutral choice without asking the user more questions.
                            - If the countries of interest are not explicitly stated you can default to no countries (country = ["none"]).

                            **Therefore, for this kind of message, do NOT ask more questions.**
                            Directly create the Talent Pool by:
                            - Selecting the original role "Chief Financial Officer" as the primary role.
                            - Selecting some example relevant roles (from your own knowledge, without asking the user).
                            - Selecting some example step_up_candidates (from your own knowledge, without asking the user).
                            - Set step_up_candidates to ["none"] if the user did not request them.
                            - Set companies to ["none"] if the user did not explicitly request them.
                            - Set gender to "Not required" if not mentioned.
                            - Set is_ethnicity_important to 0.

                    After filling all these details in the Talent Pools, call `create_talent_pool`.

                    **Hint:**
                    Some users have interacted with you before and already know the details you require. They will provide all information in one message so that you can directly create the Talent Pools. Do not complicate the experience for them by asking unnecessary questions.	
                    **Talent Pools Description Constraint:**
                    - The Talent Pools description must be between 80 and 200 words.',
            'tools' => [
            [
                'name' => 'create_talent_pool',
                'description' => 'Creates a Talent Pools in the system database based on the provided user requirements.',
                'input_schema' => [
                'type' => 'object',
                'properties' => [
                    'pool_name' => [
                    'type' => 'string',
                    'description' => 'The name of the Talent Pools. This is required.'
                    ],
                    'description' => [
                    'type' => 'string',
                    'description' => "A detailed description of the Talent Pool purpose, generated by the AI. Must be between 80 and 200 words."
                    ],
                    'target_roles' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'string'
                    ],
                    'description' => 'An array of roles for which the Talent Pools is being created. This is required.'
                    ],
                    'minimum_tenure' => [
                    'type' => 'integer',
                    'description' => 'The minimum number of years of experience required for candidates.'
                    ],
                    'step_up_candidates' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'string'
                    ],
                    'description' => 'A list of names of internal candidates who could step up into the target roles.'
                    ],
                    'alternative_roles_titles' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'string'
                    ],
                    'description' => 'A list of alternative roles titles of selected roles by the user.'
                    ],
                    'acronyms' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'string'
                    ],
                    'description' => 'A list of acronyms for the roles titles of selected roles by the user.'
                    ],
                    'companies' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'string'
                    ],
                    'description' => 'An array of companies from which external candidates might be sourced.'
                    ],
                    'gender' => [
                    'type' => 'string',
                    'enum' => ['Male', 'Female', 'Not required'],
                    'description' => 'The preferred gender of the candidates.'
                    ],
                    'country' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'string'
                    ],
                    'description' => 'The country where the candidates should be located.'
                    ],
                    'is_ethnicity_important' => [
                    'type' => 'boolean',
                    'description' => 'Indicates whether ethnicity is an important consideration in candidate selection.'
                    ],
                    'qualifications' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'string'
                    ],
                    'description' => 'A list of required qualifications for the candidates (e.g., degrees, certifications). These are suggested by the AI based on selected roles and companies.'
                    ],
                    'skills' => [
                    'type' => 'array',
                    'items' => [
                        'type' => 'string'
                    ],
                    'description' => 'A list of required skills for the candidates. These are suggested by the AI based on selected roles and companies. Do not use the word "Experience".'
                    ]
                ],
                'required' => ['pool_name', 'target_roles', 'companies']
                ]
            ]
            ],
            'messages' => $messages // Include the full conversation
        ];

      Log::info('MESSAGE RECEIVED');

        try {

            $response = Http::withHeaders([
                'x-api-key' => config('ai.anthropic.api_key'),
                'anthropic-version' => config('ai.anthropic.version'),
                'Content-Type' => 'application/json',
            ])
            ->timeout(60)
            ->post('https://api.anthropic.com/v1/messages', $payload);

            $data = $response->json();
            // Log the AI response
            Log::info('AI Response after first API call', ['aiResponse' => $data]);

            // Process the AI response

            $aiResponse = $data['content'];
            Log::info($aiResponse);


            // Add the AI's response to the conversation
            $messages[] = [
                'role' => 'assistant',
                'content' => $aiResponse
            ];

            // Check if aTalent Poolwas created
            $planCreated = false;

            $toolUseId = null;

            foreach ($aiResponse as $content) {
                if ($content['type'] === 'tool_use' && $content['name'] === 'create_talent_pool') {
                    $planCreated = true;
                    $planData = $content['input'];
                    $toolUseId = $content['id']; // Get the tool_use ID

                    // Log theTalent Pooldata
                    Log::info('Plan detected in AI response', [
                        'planCreated' => $planCreated,
                        'planData' => $planData,
                        'toolUseId' => $toolUseId,
                    ]);

                    break;
                }
            }
            Log::info($planCreated);
            //dd($planCreated);

            // If aTalent Poolwas created, execute and send back 'tool_result'
            if ($planCreated) {
                // (save theTalent Poolto database)

                // Add theTalent Pooldetails to the plans table
                $planData1 = [
                    'name'               => $planData['pool_name'],
                    'description'        => $planData['description'],
                    'minimum_Experience' => $planData['minimum_tenure'],
                    'step_up'            => "1",
                    'ethnicity'          => $planData['is_ethnicity_important'],
                    'age'                => 0,
                    'status'             => "Draft",
                    'candidate_status'   => "No Changes",
                    'user_id'            => $user->id,
                    "shared_with"        => "[]"
                ];

                $plan = Job::create($planData1);

                $planData['job_id'] = $plan->id;

                // Create the notification that aTalent Poolhas been created
                notifications::create([
                    'type'              => "TalentPool_Created",
                    'job_id'           => $plan->id,
                    'entity_name'       => $planData['pool_name'],
                    'description'       => $planData['description'],
                    'user_id'           => $user->id,
                    'user_company'      => $user->company_id
                ]);

                 //------------------  Success Requirements Table ---------------//

                // Roles are seperated by commas this expands them into a list ** this field is mandatory **
                // To store the target role
                //Log::info($planData['acronyms']);
                $successRequirementsData = [];

                // Adding target roles
                $target_roles = $planData['target_roles'];
                foreach ($target_roles as $role) {
                    $successRequirementsData[] = [
                        'job_id' => $plan->id,
                        'name'    => trim($role),
                        'type'    => 'Role',
                    ];
                }

                // Adding step-up candidates
                if  ($planData['step_up_candidates'] != ['none']) {
                $step_up_candidates = $planData['step_up_candidates'];
                    foreach ($step_up_candidates as $stepUpCandidate) {
                        $successRequirementsData[] = [
                            'job_id' => $plan->id,
                            'name'    => trim($stepUpCandidate),
                            'type'    => 'step_up',
                        ];
                    }
                }

                // Adding tenure requirement
                $minimum_tenure = $planData['minimum_tenure'];
                if ($minimum_tenure) {
                    $successRequirementsData[] = [
                        'job_id' => $plan->id,
                        'name'    => $minimum_tenure,
                        'type'    => 'Minimum_Tenure',
                    ];
                }

                // Adding location requirements
                if ($planData['country']!= ['none']) {
                    $countries = $planData['country'];
                    foreach ($countries as $country){
                            $successRequirementsData[] = [
                                'job_id' => $plan->id,
                                'name'    => $country,
                                'type'    => 'location',
                            ];
                    }

                }

                // Adding professional skills
                $skills = $planData['skills'];
                foreach ($skills as $skill) {
                    $successRequirementsData[] = [
                        'job_id' => $plan->id,
                        'name'    => trim($skill),
                        'type'    => 'professional_skill',
                    ];
                }

                /*
                // Adding educational qualifications
                $qualificationArr = $planData['qualifications'];
                foreach ($qualificationArr as $qualification) {
                    $successRequirementsData[] = [
                        'plan_id' => $plan->id,
                        'name'    => trim($qualification),
                        'type'    => 'education',
                    ];
                }
                */

                // Adding gender requirement
                if ($planData['gender'] != "Not Required") {
                    $successRequirementsData[] = [
                        'job_id' => $plan->id,
                        'name'    => trim($planData['gender']),
                        'type'    => 'Gender',
                    ];
                }

                // Adding company requirements
                if ($planData['companies'] != ['none']) {
                    $companiesArr = $planData['companies'];
                    foreach ($companiesArr as $cr) {
                        $successRequirementsData[] = [
                            'job_id' => $plan->id,
                            'name'    => trim($cr),
                            'type'    => 'Company',
                        ];
                    }
                }

                // Batch insert all records into the database
                JobRequirement::insert($successRequirementsData);

                $aiResponse = [
                    [
                        "type" => "text",
                        "text" => "The '<b>{$planData['pool_name']}</b>' has been successfully created, focusing on strategic Talent Pool for the suggested role."
                    ]
                ];
  
                //$this->ExternalSearchPerplexity($plan->id,$planData,$user);

                // Add the AI's response to the conversation
                $messages[] = [
                    'role' => 'assistant',
                    'content' => $aiResponse
                ];
                $planCreated = false;
                $request->session()->put('creating_plan', false);
                // Clear the conversation messages from the session
                $request->session()->forget('messages');
                
                // Log that messages have been cleared
                Log::info("Conversation messages cleared from session after Talent Pool creation");
                //dd("Plan created");

               Log::info("Moving onto the internal people search queue");
               // Dispatch job to queue
               
               TalentPoolExternalSearch::dispatch($planData, $user->id);

                // Near the end of createPlan, afterTalent Poolcreation is complete:
                

                return response()->json([
                    'aiResponse' => $aiResponse,
                    'choice' => "makePlan",
                    'planCreated' => $planCreated,
                    'planData' => $planData,
                ]);
            }
            else{
                // Log the AI response after the second API call
                Log::info('AI Response after second API call', ['aiResponse' => $data]);

                $aiResponse = $data['content'];

                // Add the AI's response to the conversation
                $messages[] = [
                    'role' => 'assistant',
                    'content' => $aiResponse
                ];

                // Save the updated conversation in the session
                $request->session()->put('messages', $messages);

                // Step-up List
                if ($planData['step_up_candidates'] !=['none']){
                    $rawsteps = is_array($planData['step_up_candidates']) ? $planData['step_up_candidates'] : [];
                    $psteps = array_map('trim', $rawsteps);
                }
                else{
                    $rawsteps  = [];
                    $psteps = [];
                }

                /*
                $rawKeyword = is_array($planData['keyword']) ? $planData['keyword'] : [];
                $pKeyword = array_map('trim', $rawKeyword);
                */

                // Qualifcications list
                $rawEdQual = is_array($planData['qualifications']) ? $planData['qualifications'] : [];
                $pEdQual = array_map('trim', $rawEdQual);

                // Location list
                $ploc = is_array($planData['country']) ? $planData['country'] : [$planData['country']];

                // Skills List
                $rawskill = is_array($planData['skills']) ? $planData['skills'] : [];
                $pskills = array_map('trim', $rawskill);
                $skillCount = count($pskills);
                //Log::info($skillCount);

                //Log::info($planData['country']);
                //--------------- Start the filtering for the pipeline --------------//
                if (!in_array('none', $planData['country'])){
                    $scountry = is_array($planData['country']) ? $planData['country'] : [$planData['country']];
                }
                else{
                    $scountry = [];
                }
                //Log::info($scountry);

                $scities   = "";

                Log::info("Everything is an array");

                // Getting the array for the companies using the companies, sector and industry
                $filteredCompanyIdsArr = $planData['companies'];

                // Filter the people table using target roles,
                if (!empty($proles)) {
                    $filteredPeople = People::query()
                        ->where('status', '!=', 'Submitted')
                        ->where('status', '!=', 'Reported')
                        ->where('company_id','!=',$user->company_id)
                        ->where(function ($query) use ($proles, $gen, $scountry, $planData) {
                            $query->where(function ($subquery) use ($proles) {
                                // Loop through each keyword and add an orWhere clause
                                foreach ($proles as $prole) {
                                    $subquery->orWhere('latest_role', 'like', '%' . $prole . '%');
                                }
                            });
                            if ($gen != '') {
                                $query->where('gender', $gen);
                            }
                            if ($planData['companies']!= ['none']) {
                                $query->whereIn('company_name', $planData['companies']);
                            }
                            if (!empty($scountry)) {
                                $query->whereIn('country', $scountry);
                            }
                        })
                        ->get();
                    Log::info($filteredPeople);

                    $filteredPeople = $filteredPeople->map(function ($item) {
                        $item['role_score'] = 1;
                        $item['type'] = "External-System";
                        return $item;
                    });
                } else {
                    $filteredPeople = new Collection();
                }

                $filteredPeopleidslvl1 = $filteredPeople->pluck('id');

                /* If a step up candidate has been proposed the system will only search for step candidates as well ensuring that
                people already identified from roles do not come through and uses the same filtering logic as roles
                */
                if ($rawsteps !== []) {
                    $sfilteredPeople = People::query()
                        ->where('status', '!=', 'Submitted')
                        ->where('status', '!=', 'Reported')
                        ->where('company_id','!=',$user->company_id)
                        ->whereNotIn('id', $filteredPeopleidslvl1)
                        ->when($rawsteps !== [], function ($query) use ($psteps, $gen, $scountry, $scities, $planData) {
                            $query->where(function ($subquery) use ($psteps) {
                                // Loop through each keyword and add an orWhere clause
                                foreach ($psteps as $pstep) {
                                    $subquery->orWhere('latest_role', 'like', '%' . $pstep . '%');
                                }
                            });
                            if ($gen != '') {
                                $query->where('gender', $gen);
                            }
                            if ($planData['companies']!= ['none']) {
                                $query->WhereIn('company_name', $planData['companies']);
                            }
                            // if (!empty($this->interestedCompaniesarray)){
                            //     $query->WhereIn('company_id',$this->interestedCompaniesarray);
                            // }
                            if (!empty($scountry)) {
                                $query->WhereIn('country', $scountry);
                            }

                        })
                        ->get();

                    $sfilteredPeople = $sfilteredPeople->map(function ($item) {
                        $item['role_score'] = 0.75;
                        $item['type'] = "External-System";
                        return $item;
                    });

                    Log::info($sfilteredPeople);

                    $sfilteredPeopleidslvl1 = $sfilteredPeople->pluck('id');
                } else {
                    $sfilteredPeople = [];
                    $sfilteredPeopleidslvl1 = [];
                }

                // Accronym
                $pKeyword = [];

                //Log::info("Filtering For career History");
                if (!empty($pKeyword)) {
                    $kfilteredPeople = People::query()
                        ->where('status', '!=', 'Submitted')
                        ->where('status', '!=', 'Reported')
                        ->where('company_id','!=',$user->company_id)
                        ->whereNotIn('id', $filteredPeopleidslvl1)
                        ->whereNotIn('id', $sfilteredPeopleidslvl1)
                        ->when($pKeyword !== "", function ($query) use ($pKeyword, $gen, $scountry, $scities, $planData) {
                            $query->where(function ($subquery) use ($pKeyword) {
                                // Loop through each keyword and add an orWhere clause
                                foreach ($pKeyword as $keyword) {
                                    $subquery->orWhere('latest_role', 'like', '%' . $keyword . '%');
                                }
                            });

                            if ($gen != '') {
                                $query->where('gender', $gen);
                            }
                            if ($planData['companies']!= ['none']) {
                                $query->whereIn('company_name', $planData['companies']);
                            }
                            // if (!empty($this->interestedCompaniesarray)){
                            //     $query->WhereIn('company_id',$this->interestedCompaniesarray);
                            // }
                            if (!empty($scountry)) {
                                $query->whereIn('country', $scountry);
                            }

                        })
                        ->get();
                    $kfilteredPeople = $kfilteredPeople->map(function ($item) {
                        $item['role_score'] = 0.25;
                        $item['type'] = "External-System";
                        return $item;
                    });

                    //dd($kfilteredPeople);

                    $kfilteredPeopleidslvl1 = $kfilteredPeople->pluck('id');
                } else {
                    $kfilteredPeople = [];
                    $kfilteredPeopleidslvl1 = [];
                }

                // May be relevant candidates
                $career_history_filtered = CareerHistories::query()
                ->whereNotIn('people_id', $filteredPeopleidslvl1)
                ->whereNotIn('people_id', $sfilteredPeopleidslvl1)
                ->whereNotIn('people_id', $kfilteredPeopleidslvl1)
                ->where(function ($query) use ($proles, $psteps) {
                    $query->WhereIn('role', $proles)
                        ->orWhereIn('role', $psteps);
                })
                ->get();

                // First, get the relevant IDs from $career_history_filtered
                $filteredCareerHistoryIds = $career_history_filtered->pluck('people_id');

                if ($filteredCareerHistoryIds !== null) {
                    // Then, use these IDs to filter the people table
                    $careerPeople = People::query()
                        ->whereIn('id', $filteredCareerHistoryIds)
                        ->where('company_id','!=',$user->company_id)
                        ->when($filteredCareerHistoryIds  !== null, function ($query) use ($proles, $gen, $scountry, $scities, $planData) {
                            if ($gen != '') {
                                $query->where('gender', $gen);
                            }
                            if (!empty($scountry)) {
                                $query->WhereIn('country', $scountry);
                            }
                            if (!empty($planData['companies'])) {
                                $query->WhereIn('company_name', $planData['companies']);
                            }

                            // if (!empty($this->interestedCompaniesarray)){
                            //     $query->WhereIn('company_id',$this->interestedCompaniesarray);
                            // }

                        })
                        ->get();

                    $careerPeople = $careerPeople->map(function ($item) {
                        $item['role_score'] = 0.5;
                        $item['type'] = "External-System";
                        return $item;
                    });

                    $filteredPeople = $filteredPeople->concat($sfilteredPeople);
                    $filteredPeople = $filteredPeople->concat($careerPeople);
                    $filteredPeople = $filteredPeople->concat($kfilteredPeople);
                } else {
                    $filteredPeople = $filteredPeople->concat($sfilteredPeople);
                }

                // dd($filteredPeopleWithMatches);
                //Log::info("Tenure calculation complete");

                //------------------------------  Make all the tables --------------------------------//

                // Put that data into the successionpeople list
                $dataToInsert = $filteredPeople->map(function ($person) use ($plan, $user) {
                    return [
                        'job_id'            => $plan->id,
                        'user_id'            => $user->id,
                        'people_id'          => $person->id,
                        'first_name'         => $person->forename,
                        'last_name'          => $person->surname,
                        'middle_name'        => $person->middle_name,
                        'other_name'         => $person->other_name,
                        'gender'             => $person->gender,
                        'diverse'            => $person->diverse,
                        'location'           => $person->country,
                        'country'            => $person->country,
                        'city'               => $person->city,
                        'summary'            => $person->summary,
                        'linkedinURL'        => $person->linkedinURL,
                        'latest_role'        => $person->latest_role,
                        'company_id'         => $person->company_id,
                        'company_name'       => $person->company_name,
                        'start_date'         => $person->start_date,
                        'end_date'           => $person->end_date,
                        'tenure'             => $person->tenure,
                        'function'           => $person->function,
                        'division'           => $person->division,
                        'seniority'          => $person->seniority,
                        'exco'               => $person->exco,
                        'career_history'     => $person->career_history,
                        'educational_history' => $person->educational_history,
                        'skills'             => $person->skills,
                        'languages'          => $person->languages,
                        'skills_match'       => $person->skill_score,
                        'education_match'    => 0,
                        'location_match'     => 0,
                        'role_match'         => 0,
                        'readiness'          => $person->readiness,
                        'other_tags'         => $person->other_tags,
                        'gender_match'       => 0,
                        'tenure_match'       => 0,
                        'total_score'        => 0,
                        'people_type'        => $person->type,
                    ];
                })->toArray(); // Convert to a plain array

                if (!empty($dataToInsert)) {
                    // Break the data into chunks of 1000 (adjust the chunk size as needed)
                    foreach (array_chunk($dataToInsert, 1000) as $chunk) {
                        pipeline::insert($chunk);
                    }
                }

                // Prepare the tool_result message
                $toolResult = [
                    'type' => 'tool_result',
                    'tool_use_id' => $toolUseId,
                    'content' => "Talent Pool '{$planData['plan_name']}' has been successfully created in the database."
                ];

                // Add the tool_result to messages
                $messages[] = [
                    'role' => 'user',
                    'content' => [$toolResult]
                ];

                // Prepare the payload with updated messages
                $payload['messages'] = $messages;

                // Log the messages before the second API call
                Log::info('Messages before second API call', ['messages' => $messages]);

                $startTime = microtime(true);
                // Make another API call to get the final AI response
                $response = Http::withHeaders([
                    'x-api-key' => config('ai.anthropic.api_key'),
                    'anthropic-version' => config('ai.anthropic.version'),
                    'Content-Type' => 'application/json',
                ])
                ->timeout(60)
                ->post('https://api.anthropic.com/v1/messages', $payload);
                $endTime = microtime(true);
                $executionTime = $endTime - $startTime;

                Log::info($executionTime);
                $data = $response->json();

                // Log the AI response after the second API call
                Log::info('AI Response after second API call', ['aiResponse' => $data]);

                $aiResponse = $data['content'];

                // Add the AI's response to the conversation
                $messages[] = [
                    'role' => 'assistant',
                    'content' => $aiResponse
                ];
            }

            // Save the updated conversation in the session
            $request->session()->put('messages', $messages);

            // Log the final plan data and planCreated flag
            Log::info('Final planCreated and planData', [
                'planCreated' => $planCreated,
                'planData' => $planData,
            ]);

            // Return a JSON response for AJAX

            return response()->json([
                'aiResponse' => $aiResponse,
                'planCreated' => $planCreated,
                'planData' => $planData,
            ]);
        }

         catch (\Exception $e) {
            Log::error('Error in sendMessage', ['exception' => $e]);
            if ($planData = []){
                $problem = "Sorry I'm running into an issue please refresh the page or try again later";
            }
            else{
                $problem = "I have created the Talent Pool just running into difficulties showing the review button could you please go to your Talent Pool page it should be the first one you see";
            }
            Log::error('Error in sendMessage', ['exception' => $e]);

            $aiResponse = [
                [
                    'type' => 'text',
                    'text' => $problem,
                ]
            ];

            return response()->json([
                'aiResponse' => $aiResponse,
                'planCreated' => null,
                'planData' => null,
            ]);

        }
    }



}