<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Http\Request;
use App\Models\SuccessionPlan;
use App\Models\Location;
use App\Models\User;
use App\Models\SuccessPeople;
use App\Models\InternalPeople;
use App\Models\CareerHistories;
use App\Models\Company;
use App\Models\internal_career_histories;
use App\Models\SuccessRequirements;
use App\Models\SuccessSkills;
use App\Models\People;
use App\Models\Skills;
use App\Models\Job;
use App\Models\JobRequirement;
use App\Models\Account;
use App\Models\pipeline;
use App\Models\notifications;
use App\Models\PlanScores;
use App\Models\UserNotes;
use App\Models\Recruitment;
use App\Models\RecruitmentPipeline;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Models\JobPeople;
use Illuminate\Support\Facades\Auth;
use App\Models\RecruitmentStage;
use Illuminate\Support\Facades\Mail;
use App\Mail\ShareWithNotification;



use function PHPSTORM_META\type;

class SummaryTables extends Component
{
    protected $listeners = [
        'modalClosed',
        'removeSuccessPerson',
        'approveSuccessPerson',
        'deleteUserNote',
        'viewDetailModalClosed', 
        'recruitModalClosed',
        'SaveProject',
    ];

    public $plan;
    public $user;
    public $jobs;
    public $selectedJobs = [];

    // Public references 
    public $name = '';
    public $type = '';
    public $showForm = false;

    //References to update plan details
    public $plan_name;
    public $description;
    public $status = 'Draft';
    public $minExp;
    public $roles;
    public $education;
    public $location;
    public $selectedCountries = [];
    public $countries;
    public $contributers;
    public $selectedcontributers;
    public $successArray;
    public $companies;
    public $selectedCompanies   = [];

    //References for tables
    public $successpeople;
    public $pipelinepeople;
    public $isPipelineMoverExist = false;
    public $awards;

    // References for charts
    public $genderLabels = [];
    public $genderData = [];
    public $typeLabels = [];
    public $typeData = [];

    public $companyLabels = [];
    public $companyData = [];
    public $functionLabels = [];
    public $functionData = [];
    public $divisionLabels = [];
    public $divisionData = [];
    public $roleLabels = [];
    public $roleData = [];
    public $statusLabels = [];
    public $statusData = [];

    // Variables for Individual Success People
    public $savedPeople = [];
    public $skills;
    public $SuccessSkills;

    // Chart labels and data
    public $scoreLables = [];
    public $scoreData = [];

    // Update Scores variables
    public $updatelocScore;
    public $updategenScore;
    public $updaterolScore;
    public $updateskillScore;
    public $updatetenureScore;
    public $filteredPnotes;
    public $successPersonNotes;
    public $successPersonSummary;

    public $step = 1;
    public $troles = [];
    public $selectedStatuses    = [];
    public $selectedColleagues  = [];
    public $selectedTaggedRole  = null;
    public $descriptions        =    '';
    public $enteredRoles        =   [];
    public $roleTag             =   '';
    public $minimum_Experience  =    null;
    public $stepup              =    [];
    public $keyword              =    [];

    public $stepupTag           =    '';
    public $qualificationTag    =   '';
    public $qualifications      =   [];
    public $newSkillTag         =   '';
    public $newSkills           =   [];
    public $ethnicity           =    0;
    public $gender              =    null;
    public $peoplescareer;
    public $newSkillData = [
        'qualifications'  => [],
        'skills'          => [],
        'targetRoles'     => [],
        'stepUpCandidate' => [],
        'keyword'         => []
    ];
    public $interestedCompaniesarray = [];
    public $sectors = [];
    public $industries = [];
    public $selectedSectors     =    [];
    public $selectedIndustries  =    [];
    public $editPlanPopup = false;
	public $selectedProject; 
	public $selectedpersonId;
	public $recruitmentTitle;
	public $interviewCount = 3;
	public $interviewfields = [];
	
    public function mount()
    {
		$this->initializeFields();

		
        $this->user = auth()->user();
        $user = $this->user;
        /*
        $data = $this->dataPreloader->preloadPeople($this->user);
        $this->companies = $data['companies']; // Correct
        $this->industries = $data['industries'];
        */
        $cacheKey = "target_{$this->user->id}_comps";

        //*
        //$this->interestedCompaniesarray = $companyIds;

        $interestedCompanies = false;
        $interestedIndustries = false;
        $interestedSectors = false;
        $accountObj = Account::where('id', $this->user->account_id)->first();

        if($accountObj){
            if($accountObj->company_of_interest && $accountObj->company_of_interest != ""){
                $interestedCompanies = explode(",", $accountObj->company_of_interest);
            }
            else if($accountObj->industry_interest && $accountObj->industry_interest != ""){
                    $interestedIndustries = explode(",", $accountObj->industry_interest);
            }
            else if($accountObj->sector_interest && $accountObj->sector_interest != ""){
                $interestedSectors = explode(",", $accountObj->sector_interest);
            }
        }

        $companyIds = [];
        if($interestedCompanies && count($interestedCompanies) > 0){
            $companyIds = $interestedCompanies;
        }
        else if($interestedIndustries && count($interestedIndustries) > 0){
            $companyIds = Company::whereIn('industry', $interestedIndustries)->pluck('id')->toArray();
        }
        else if($interestedSectors && count($interestedSectors) > 0){
            $companyIds = Company::whereIn('sector', $interestedSectors)->pluck('id')->toArray();            
        }

       $this->companies = Company::select(['id','name','industry','sector'])
                                    ->where('id', '!=', $this->user->company_id)
                                    ->when(!empty($companyIds),function($query) use($companyIds){
                                        $query->whereIn('id', $companyIds);
                                    })
                                    ->get()->map(function ($company) {
            return [
                'value' => $company->name,
                'label' => $company->name,
                'industry' => $company->industry,
                'sector' => $company->sector
            ];
        })->toArray();
            
        $industries = Company::select(['industry'])
                     ->whereNotNull('industry')
                     ->where('industry', '!=', 'NA')
                     ->where('industry', '!=', '0')
                     ->where('industry', '!=', '')
                     ->when(!empty($companyIds), function($query) use($companyIds) {
                         $query->whereIn('id', $companyIds);
                     })
                     ->distinct()
                     ->orderBy('industry', 'ASC')
                     ->get()
                     ->pluck('industry') // Extract the "industry" column values
                     ->toArray(); // Convert to an array

        // Transform industries into the desired structure
        $this->industries = array_map(function ($industry) {
                return [
                    'value' => $industry,
                    'label' => $industry,
                ];
            }, $industries);

        //dd($this->industries);

        Log::info($this->plan);
        $this->user = auth()->user();
        $user = auth()->user();
        //$this->countries = Location::distinct()->pluck('country_name')->toArray();

        $this->jobs = Job::where('user_id', $user->id)->get();

        $this->jobs = Job::where('user_id', $user->id)
            //->whereNotIn('id',$user->id)
            ->distinct()
            ->pluck('name')
            ->map(function ($job) {
                return ['value' => $job, 'label' => $job];
            })
            ->toArray();

        //------------------Type Spit Chart-------------------//
        $typeCountsData = DB::table('success_people')
            ->select('type', DB::raw('COUNT(*) as total_people'))
            ->where('plan_id', $this->plan->id)
            ->groupBy('type')
            ->get();
        //dd($peopleCountsData);

        foreach ($typeCountsData as $data) {
            $this->typeLabels[] = $data->type;
            $this->typeData[] = $data->total_people;
        }

        //-------------- Exco --------------------------//
        $statusCountsData = DB::table('success_people')
            ->select('exco', DB::raw('COUNT(*) as total_people'))
            ->where('plan_id', $this->plan->id)
            ->groupBy('exco')
            ->get();
        //dd($peopleCountsData);

        foreach ($statusCountsData as $data) {
            $this->statusLabels[] = $data->exco;
            $this->statusData[] = $data->total_people;
        }


        //------------------Gender Spit Chart-------------------//
        $peopleCountsData = DB::table('success_people')
            ->select('gender', DB::raw('COUNT(*) as total_people'))
            ->where('plan_id', $this->plan->id)
            ->groupBy('gender')
            ->get();
        //dd($peopleCountsData);

        foreach ($peopleCountsData as $data) {
            $colorCode = "#8B5CF6";
            if ($data->gender == "Male") {
                $colorCode = "#3B82F6";
            } else if ($data->gender == "Female") {
                $colorCode = "#FFA347";
            }
            $this->genderLabels[] = $data->gender;
            $this->genderData[] = [
                "x" => $data->gender,
                "y" => $data->total_people,
                "fillColor" => $colorCode
            ];
        }
        // dd($this->genderData);
        //------------------Companies Split Chart---------------//
        $companyCountsData = DB::table('success_people')
            ->select('company_name', DB::raw('COUNT(*) as total_company'))
            ->where('plan_id', $this->plan->id)
            ->whereNotNull('company_name')
            ->where('company_name', '!=', '')
            ->groupBy('company_name')
            ->get();

        // dd($companyCountsData);

        foreach ($companyCountsData as $cdata) {
            $this->companyLabels[] = $cdata->company_name;
            $this->companyData[] = $cdata->total_company;
        }

        //-----------------Pipeline & Success People-----------//
        $this->successpeople = SuccessPeople::where('plan_id', $this->plan->id)
            ->orderBy('total_score', 'desc')
            ->get();
        // dd($this->successpeople);

        $this->successArray = $this->successpeople->pluck('people_id')->toArray();

        $this->pipelinepeople = DB::table('pipelines')
                                ->where('plan_id', $this->plan->id)
                                ->whereNotIn('people_id', $this->successArray)
                                ->take(10)
                                ->get();

        //dd($this->pipelinepeople);

        $pipelineMoverCount = DB::table('pipelines')
                                    ->where('plan_id', $this->plan->id)
                                    ->whereNotIn('people_id', $this->successArray)
                                    ->where('mover', 'Mover')
                                    ->count();
        $this->isPipelineMoverExist = $pipelineMoverCount > 0 ? true : false;

        //$this->selectedcontributers = SuccessionPlan::where('id', $this->plan->id)
        //    ->pluck('shared_with')[0];
        // dd($this->user);


        // $sectors = Company::whereNotNull('sector')
        //                     ->where('sector', '!=', 'NA')
        //                     ->where('sector', '!=', '0')
        //                     ->when(!empty($this->interestedCompaniesarray), function($query){
        //                         $query->WhereIn('id',$this->interestedCompaniesarray);
        //                     })
        //                     ->distinct()
        //                     ->orderBy('sector', 'ASC')
        //                     ->get(['id', 'sector'])
        //                     ->groupBy('sector')
        //                     ->toArray();
        // foreach($sectors as $key => $sector){
        //     $this->sectors[] = ['value' => $key, 'label' => $key];
        // }

        // $this->selectedcontributers = json_decode($this->selectedcontributers);
        // dd($this->selectedcontributers);
        // $contributers = User::whereIn('name',$this->selectedcontributers)
        // ->where('id',$user->id)
        // ->pluck('id')->toArray();

        // dd($this->selectedcontributers);

        //----------------- Success People Summary --------------------//
        /*        
        $highestScorer = DB::table('success_people')
        ->select('id', DB::raw('"highest scorer" as award'))
        ->where('plan_id', $this->plan->id)
        ->orderBy('total_score', 'desc')
        ->take(1);
        

        $highestSkilled = DB::table('success_people')
        ->select('id', DB::raw('"highest skilled" as award'))
        ->where('plan_id', $this->plan->id)
        ->orderBy('skills_match','desc')
        ->take(1);
        

        $longestTenure = DB::table('success_people')
        ->select('id', DB::raw('"longest_tenure" as award'))
        ->where('plan_id', $this->plan->id)
        ->orderBy('tenure','desc')
        ->take(1);
        

        $topPerformers = $highestScorer->union($highestSkilled)
                                       ->union($longestTenure)
                                       ->get();
        
        $combinedResults = $topPerformers->groupBy('id')->map(function ($grouped) {
        $awards = $grouped->pluck('award')->implode(', '); // Concatenate awards
        return [
            'id' => $grouped->first()->id, // Use the ID from the first row
            'award' => $awards,
              ];
        })->values()->all();
        

        $topsuccess = DB::table('success_people')
        ->where('plan_id', $this->plan->id)
        ->orderBy('total_score','desc')
        ->take(10)
        ->get();

        $this->successpeople = $topsuccess->map(function($person) use ($combinedResults) {
            $award = collect($combinedResults)->firstWhere('id', $person->id);
            $person->award = $award ? $award['award'] : null;
            return $person;
        });
        */

        //-------------------------- Function Split -------------------------------//
        $functionCountsData = DB::table('success_people')
            ->select('function', DB::raw('COUNT(*) as total_people'))
            ->where('plan_id', $this->plan->id)
            ->whereNotNull('function')
            ->where('function', '!=', '')
            ->groupBy('function')
            ->orderBy('total_people', 'desc')
            ->get();

        foreach ($functionCountsData as $data) {
            $this->functionLabels[] = $data->function;
            $this->functionData[] = $data->total_people;
        }


    }

    public function render()
    {
        $user = $this->user;
        $this->SuccessSkills = SuccessSkills::where("succession_plan_id", $this->plan->id)
            ->get();

        $this->countries = Location::distinct()->pluck('country_name')
            ->map(function ($country) {
                return ['value' => $country, 'label' => $country];
            })->toArray();

        $this->contributers = User::where('company_id', $user->company_id)
            ->get()->map(function ($user) {
                return [
                    'value' => $user->id,
                    'label' => $user->name,
                ];
            })->toArray();    

        $requirements = DB::table('success_requirements')->where('plan_id', $this->plan->id)->get();
        //dd($requirements);

        $plandetails = SuccessionPlan::where('id', $this->plan->id)->get();

        // //Getting the model inputs for the form
        // $this->name = $plandetails[0]->name;
        // $this->descriptions = $plandetails[0]->description;
        // $this->selectedTaggedRole = $plandetails[0]->tagged_individual;
        // $this->selectedColleagues = $plandetails[0]->shared_with ? json_decode($plandetails[0]->shared_with) : [];
        // $this->ethnicity = $plandetails[0]->ethnicity;

        $plan_users = DB::table('succession_plans')
            ->where('id', $this->plan->id)
            ->pluck('shared_with');

        if (!empty($plan_users[0]) && $plan_users[0] != "[]" && $plan_users[0] != "") {
            // dd($plan_users);
            $decoded_users = json_decode($plan_users[0], true); // Decode JSON into an associative array
            // dd($plan_users);
            if (is_array($decoded_users) && !empty($decoded_users)) {
                $shared_users = User::whereIn('id', $decoded_users)->pluck('name');
            } else {
                $shared_users = collect([]);
            }
        } else {
            // dd($plan_users);
            $shared_users = collect([]);
        }

        //dd($shared_users);

        $plan = $this->plan[0];


        $successPeople = DB::table('success_people')
            ->select('plan_id', DB::raw('Count(*) as successcount'))
            ->groupby('plan_id')
            ->where('plan_id', $this->plan->id)
            ->get();

        $successCountsMap = $successPeople->keyBy('plan_id');

        $this->plan->successcount = $successCountsMap[$this->plan->id]->successcount ?? 0;

        /*------------------------------------------------------------------------------------------------------------------------------------
                                                        GET THE SUCCESS PEOPLE
        --------------------------------------------------------------------------------------------------------------------------------------*/
        // Get the list of success people
        $finalPeople =  SuccessPeople::rightJoin('succession_plans', 'success_people.plan_id', '=', 'succession_plans.id')
            ->where('succession_plans.id', $this->plan->id)
            ->get(['success_people.*', 'succession_plans.name as plan_name', 'succession_plans.description']);

        // Get the variables for looking at a specific individual
        $filteredPeople =  SuccessPeople::rightJoin('succession_plans', 'success_people.plan_id', '=', 'succession_plans.id')
            ->leftJoin('people', 'success_people.people_id', '=', 'people.id')
            ->where('succession_plans.id', $this->plan->id)
            ->where('success_people.id', $this->savedPeople)
            ->get(['success_people.*', 'succession_plans.name as plan_name', 'succession_plans.description', 'people.company_name as current_company', 'people.latest_role as current_role']);
             

            
        // Get the variables for the visuals for an individual person
        if ($filteredPeople->isNotEmpty()) {
            $skills_list = $filteredPeople ? Skills::where('people_id', $filteredPeople[0]->people_id)->get() : collect();
          
             
            // Skills
            $scoreskills = $this->SuccessSkills->whereIn('success_people_id', $filteredPeople[0]->id);
            //dd($scoreskills);

            $groupedSkillScores = $scoreskills->groupBy('success_people_id');
            //dd($groupedSkillScores);

            $groupedSkillScores->transform(function ($item) {
                return [

                    'skill_name' => $item->pluck('skill_name')->toArray(),
                    'scores' => $item->pluck('score')->toArray(),
                ];
            });
            //dd($groupedSkillScores);

            // Loop through the plans and attach corresponding data from groupedPlanScores
            $filteredPeople->each(function ($person) use ($groupedSkillScores) {
                $PersonId = $person->id; // Replace with the actual column name
                if (isset($groupedSkillScores[$PersonId])) {
                    $person->scoreLabels = $groupedSkillScores[$PersonId]['skill_name'];
                    $person->scoreData = $groupedSkillScores[$PersonId]['scores'];
                } else {
                    $person->scoreLabels = [];
                    $person->scoreData = [];
                }
            });

            //Getting the career histories
            $filterExternal = $filteredPeople->pluck('people_id');
            $filteredPeople->each(function ($person) use ($skills_list) {
                $person->skills_list = $skills_list ? $skills_list->toArray() : null;
            });   
            // Loop through the filtered people and attach corresponding data
            $filteredPeople->each(function ($person) {
                // Initialize arrays to store match labels and scores
                $matchLabels = [];
                $matchData = [];

                $matchLabels[] = 'Skills';  // Add label for skills
                $matchData[] = $person->skills_match;

                $matchLabels[] = 'Roles';
                $matchData[] = $person->role_match;

                $matchLabels[] = 'Location';
                $matchData[] = $person->location_match;

                $matchLabels[] = 'Gender';
                $matchData[] = $person->gender_match;

                $matchLabels[] = 'Tenure';
                $matchData[] = $person->tenure_match;

                // Assign the match_labels and match_data fields to the person
                $person->match_labels = $matchLabels;
                $person->match_data = $matchData;
                
            });
        }
        $user = $this->user;
        $taggedPeople = SuccessionPlan::where('user_id', $user->id)
            ->orWhere(function ($query) use ($user) {
                $query->whereRaw("FIND_IN_SET(?, shared_with)", [$user->id]);
            });

        $taggedPeople = $taggedPeople->pluck('tagged_individual')->filter();

        // Use the plucked and filtered array in the query if it's not empty
        // if ($taggedPeople->isNotEmpty()) {
        //     $this->troles = InternalPeople::select('id','forename','surname','latest_role')
        //         ->where('company_id', $user->company_id)
        //         ->whereNotIn('user_id', $taggedPeople)
        //         ->get()->map(function ($person) {
        //             return [
        //                 'value' => $person->id,
        //                 'label' => $person->forename . ' ' . $person->surname . ' (' . (!empty($person->latest_role) ? $person->latest_role : "Not Provided") . ')',
        //             ];
        //         })->toArray();
        // } else {
        //     // Handle the case when $taggedPeople is empty or only contains null values
        //     // For example, you might want to return all records in this case
        //     $this->troles = InternalPeople::select('id','forename','surname','latest_role')
        //     ->where('company_id', $user->company_id)
        //         ->get()->map(function ($person) {
        //             return [
        //                 'value' => $person->id,
        //                 'label' => $person->forename . ' ' . $person->surname . ' (' . (!empty($person->latest_role) ? $person->latest_role : "Not Provided") . ')',
        //             ];
        //         })->toArray();
        // }

 
    $accountIds = Account::whereNotNull('relationship_manager_id')
        ->whereRaw("FIND_IN_SET(?, relationship_manager_id)", [$user->id])
        ->pluck('id');
       
    $accountUsers = User::whereIn('account_id', $accountIds)
        ->whereNull('deleted_at')
        ->get();
    
    $companyUsers = User::where('company_id', $user->company_id)
        ->where('id', '!=', $user->id)
        ->whereNull('deleted_at')
        ->get();
    
        $mergedUsers = $accountUsers->merge($companyUsers)->unique('id');

        $colleagues = $mergedUsers->map(function ($user) {
            return [
                'value' => $user->id,
                'label' => $user->name.' (' . $user->email . ')',
            ];
        })->values()->toArray();
        

/*
        if (in_array($user->role, ['Admin', 'Master'])) {
            $colleagues = User::all()
            ->map(function ($user) {
            return [
                'value' => $user->id,
                'label' => $user->name,
            ];
            })
            ->toArray();
        } else {
            $colleagues = User::where('company_id', $user->company_id)
            ->where('id', '!=', $user->id)
            ->get()
            ->map(function ($user) {
                return [
                    'value' => $user->id,
                    'label' => $user->name,
                ];
            })
            ->toArray();
        }
*/
        $internalPeoplesCount = InternalPeople::where('company_id', $this->user->company_id)->count();

        if ($this->savedPeople) {
            $userNotes = UserNotes::select('user_notes.*', 'users.name as user_name')
                ->where('entity_id', $this->savedPeople)
                ->where('entity_type', 'success_person')
                ->join('users', 'user_notes.author', '=', 'users.id')
                ->orderBy('user_notes.id', 'desc')
                ->get();

            $redSuc = SuccessPeople::where('id', $this->savedPeople)->first();

            $this->updategenScore = $this->updategenScore ?? $redSuc->gender_match;
            $this->updaterolScore = $this->updaterolScore ?? $redSuc->role_match;
            $this->updatetenureScore = $this->updatetenureScore ?? $redSuc->tenure_match;
            $this->updatelocScore = $this->updatelocScore ?? $redSuc->location_match;
            $this->updateskillScore = $this->updateskillScore ?? $redSuc->skills_match;
            // $this->filteredPnotes = $filteredPeople[0]->notes && $filteredPeople[0]->notes != '' && $filteredPeople[0]->notes != 'Enter notes here' ? $filteredPeople[0]->notes : null;
            $this->successPersonNotes = $userNotes;
            $this->successPersonSummary = $filteredPeople && isset($filteredPeople[0]) ? $filteredPeople[0]->summary : null;
        }
		// $recruitments_projects = DB::table('recruitments')->where('user_id', $user->id)->get();
		// $recruitments_projects = DB::table('recruitments')
								// ->leftJoin('RecruitmentPipeline', 'recruitments.id', '=', 'RecruitmentPipeline.recruitment_project_id')
								// ->where('recruitments.user_id', $user->id)
								// ->where(function($query) {
										// $query->where('recruitments.status', '!=', 'archived')
											  // ->orWhereNull('recruitments.status');
								// })
								// ->where(function($query) {
									// $query->whereNull('RecruitmentPipeline.Plan_id')
										  // ->orWhere('RecruitmentPipeline.Plan_id', '')
										  // ->orWhere('RecruitmentPipeline.Plan_id', $this->plan->id);
								// })
								// ->select('recruitments.*') 
								// ->distinct()  
								// ->get();
	
		$recruitmentProjectId = '';
		$planRecruitmentPipeline = RecruitmentPipeline::where('Plan_id', $this->plan->id)->first();
		if ($planRecruitmentPipeline) {
			$recruitmentProjectId = $planRecruitmentPipeline->recruitment_project_id;
		}
        
        return view('livewire.summary-tables', compact('requirements', 'plandetails', 'plan', 'filteredPeople', 'shared_users', 'colleagues', 'internalPeoplesCount', 'recruitmentProjectId'));
    }

    public function refreshMover($successPeople){

        $people = People::where('id', $successPeople['people_id'])->first();

        // Update success people
        $successPeopleDataToUpdate = [
            'first_name' => $people->forename,
            'last_name' => $people->surname,
            'middle_name' => $people->middle_name,
            'other_name' => $people->other_name,
            'gender' => $people->gender,
            'diverse' => $people->diverse,
            'country' => $people->country,
            'city' => $people->city,
            'linkedinURL' => $people->linkedinURL,
            'latest_role' => $people->latest_role,
            'exco' => $people->exco,
            'company_id' => $people->company_id,
            'company_name' => $people->company_name,
            'start_date' => $people->start_date,
            'end_date' => $people->end_date,
            'tenure' => $people->tenure,
            'function' => $people->function,
            'division' => $people->division,
            'seniority' => $people->seniority,
            'career_history' => $people->career_history,
            'educational_history' => $people->educational_history,
            'skills' => $people->skills,
            'languages' => $people->languages,
            'other_tags' => $people->other_tags,
            'mover' => 'Non Mover',
            'readiness' => $people->readiness,
            'summary' => $people->summary
        ];

        SuccessPeople::where('id', $successPeople['id'])->update($successPeopleDataToUpdate);

        // Update pipeline
        $pipelineDataToUpdate = [
            'first_name' => $people->forename,
            'last_name' => $people->surname,
            'middle_name' => $people->middle_name,
            'other_name' => $people->other_name,
            'gender' => $people->gender,
            'diverse' => $people->diverse,
            'country' => $people->country,
            'city' => $people->city,
            'mover' => 'Non Mover',
            'linkedinURL' => $people->linkedinURL,
            'latest_role' => $people->latest_role,
            'company_id' => $people->company_id,
            'company_name' => $people->company_name,
            'exco' => $people->exco,
            'start_date' => $people->start_date,
            'end_date' => $people->end_date,
            'tenure' => $people->tenure,
            'function' => $people->function,
            'division' => $people->division,
            'seniority' => $people->seniority,
            'career_history' => $people->career_history,
            'educational_history' => $people->educational_history,
            'skills' => $people->skills,
            'languages' => $people->languages,
            'other_tags' => $people->other_tags,
            'readiness' => $people->readiness,
            'summary' => $people->summary
        ];

        pipeline::where('people_id', $successPeople['people_id'])
                    ->where('plan_id', $this->plan->id)
                    ->update($pipelineDataToUpdate);

        $pipelineMoverCount = pipeline::where('plan_id', $this->plan->id)
                                ->where('mover', 'Mover')
                                ->count();

        $successPeopleMoverCount = SuccessPeople::where('plan_id', $this->plan->id)
                                    ->where('mover', 'Mover')
                                    ->count();

        if($pipelineMoverCount <= 0 && $successPeopleMoverCount <= 0){
            SuccessionPlan::where('id', $this->plan->id)->update(['mover' => 'Non Mover']);
        }

        return;
    }

    public function createRequirement()
    {
        // Validate the requirement name and perform any other validation if needed

        // Create the requirement
        SuccessRequirements::create([
            'plan_id' => $this->plan->id,
            'name' => $this->name,
            'type' => 'Additional'
            // Add other requirement properties here
        ]);

        // Clear the form fields and hide the form
        $this->reset('name', 'type', 'showForm');
    }

    public function updatePlan()
    { 
          // Define validation rules
        $rules = [
            'name' => 'required|string',
            'descriptions' => 'required|string'
        ];

        // Validate the data
        $validator = Validator::make($this->validate($rules), $rules);

        // Check if validation fails
        if ($validator->fails()) {
            // Reset step to 1 if validation fails
            $this->step = 1;
            return;
        }
        $successPeople = SuccessPeople::where('plan_id', $this->plan->id);
        // dd($this->minimum_Experience);
        $pipelinePeople = pipeline::where('plan_id', $this->plan->id);
        $peopleIdsArr = $pipelinePeople->pluck('people_id')->toArray();
        $plan = SuccessionPlan::find($this->plan->id);
        $user = auth()->user();

        $dataToUpdate = [
            'name'               => $this->name,
            'description'        => $this->descriptions,
            'minimum_Experience' => $this->minimum_Experience,
            'ethnicity'          => $this->ethnicity,
            'tagged_individual'  => $this->selectedTaggedRole,
            'status'             => $this->status,
            "shared_with"        => "[]"
        ];

        // To store the collegues or contributors
        if (!empty($this->selectedColleagues)) {
            // No need to fetch user ids again becuase we are getting user ids already through the form
            // $contributers = User::whereIn('name', $this->selectedColleagues)
            //                     ->where('id', $user->id)
            //                     ->pluck('id')->toArray();
            $dataToUpdate["shared_with"] = json_encode($this->selectedColleagues);
              
        }
// dd($this->plan->id);
        $plan->update($dataToUpdate);
        if (!empty($this->selectedColleagues)) {
            $selectedUsers = User::whereIn('id', $this->selectedColleagues)->get();

            foreach ($selectedUsers as $selectedUser) {
                $sendersName = $selectedUser->name;
                $email = $selectedUser->email;

                $title = "Plan Shared Notification";
                $currentUserName = auth()->user()->name;
                $emailContent = "Dear {$sendersName},\n\nThis is to notify you that the plan '{$this->plan->name}' has been shared with you by {$currentUserName}.";
                if ($email) {
                    Mail::to($email)->send(new ShareWithNotification($currentUserName, $title, $emailContent));
                }
            }
        }
        SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'role')->delete();
        // Add the new requirements into success requirements
        if (!empty($this->newSkillData['targetRoles'])) {

            foreach ($this->newSkillData['targetRoles'] as $role) {
                $role = trim($role);
                SuccessRequirements::create([
                    'plan_id' => $plan->id,
                    'name' => $role,
                    'type' => "Role",
                ]);
            }
            // SuccessRequirements::insert($rolesarray);
        }
        $rawRoles = $this->newSkillData['targetRoles'];
        $proles = array_map('trim', $rawRoles);

        SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'step_up')->delete();
        // Getting any step_up candidates
        foreach ($this->newSkillData['stepUpCandidate'] as $stepUpCandidate) {
            $role = trim($stepUpCandidate);
            SuccessRequirements::create([
                'plan_id' => $plan->id,
                'name' =>  trim($stepUpCandidate),
                'type' => "step_up",
            ]);
        }

        //Field keyword
        SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'keyword')->delete();
        foreach ($this->newSkillData['keyword'] as $keywordText) {
            SuccessRequirements::create([
                'plan_id' => $plan->id,
                'name' =>  trim($keywordText),
                'type' => "keyword",
            ]);
        }
        SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'professional_skill')->delete();
        // if (!empty($this->newSkills)) {
        //     $skillarray = [];
        //     foreach ($this->newSkills as $sr) {
        //         $skillarray[] = [
        //             'plan_id' => $this->plan->id,
        //             'name'    => trim($sr),
        //             'type'    => 'professional_skill',
        //         ];
        //     }
        //     SuccessRequirements::insert($skillarray);
        // }

        SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'education')->delete();
        // if (!empty($this->qualifications)) {
        //     $qualificationarray = [];
        //     foreach ($this->qualifications as $sr) {
        //         $qualificationarray[] = [
        //             'plan_id' => $this->plan->id,
        //             'name'    => trim($sr),
        //             'type'    => 'education',
        //         ];
        //     }
        //     SuccessRequirements::insert($skillarray);
        // }

        // Get any another skills into the requirements table
        foreach ($this->newSkillData['skills'] as $skill) {
            SuccessRequirements::create([
                'plan_id' => $plan->id,
                'name' => trim($skill),
                'type' => "professional_skill"
            ]);
        }

        // This will be use for qualifications
        // Getting any Educational Qualifications Requirements
        foreach ($this->newSkillData['qualifications'] as $qualification) {
            SuccessRequirements::create([
                'plan_id' => $plan->id,
                'name' => trim($qualification),
                'type' => "education"
            ]);
        }

        SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'location')->delete();
        if (!empty($this->selectedCountries)) {
            foreach ($this->selectedCountries as $ucountry) {
                SuccessRequirements::create([
                    'plan_id' => $this->plan->id,
                    'name' => $ucountry,
                    'type' => 'location'
                    // Add other requirement properties here
                ]);
            }
        }

        SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'Minimum_Tenure')->delete();
        if (!empty($this->minExp)) {
            SuccessRequirements::create([
                'plan_id' => $this->plan->id,
                'name' => $this->minExp[0],
                'type' => 'Minimum_Tenure'
                // Add other requirement properties here
            ]);
        }

        // Because a plan may have only one gender
        if ($this->gender) {
            SuccessRequirements::updateOrInsert(
                ['plan_id' => $this->plan->id, 'type' => 'Gender'],
                [
                    'plan_id' => $this->plan->id,
                    'name'    => trim($this->gender),
                    'type'    => 'Gender',
                ]
            );
        } else {
            SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'Gender')->delete();
        }

        SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'Company')->delete();
        if (!empty($this->selectedCompanies)) {
            $companyarray = [];
            foreach ($this->selectedCompanies as $cr) {
                $companyarray[] = [
                    'plan_id' => $this->plan->id,
                    'name'    => trim($cr),
                    'type'    => 'Company',
                ];
            }
            SuccessRequirements::insert($companyarray);
        }

        //SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'Sector')->delete();
        // if (!empty($this->selectedSectors)) {
        //     foreach ($this->selectedSectors as $sector) {
        //         SuccessRequirements::create([
        //             'plan_id' => $plan->id,
        //             'name'    => trim($sector),
        //             'type'    => 'Sector',
        //         ]);
        //     }
        // }

        SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'Industry')->delete();
        if (!empty($this->selectedIndustries)) {
            foreach ($this->selectedIndustries as $industry) {
                SuccessRequirements::create([
                    'plan_id' => $plan->id,
                    'name'    => trim($industry),
                    'type'    => 'Industry',
                ]);
            }
        }

        // $SuccessRequirements = SuccessRequirements::where('plan_id',$this->plan->id);

        /*-------------------------  Get the scores updated for individuals in the plans  -----------------------*/
        // Update the scoring for role matches
        // dd($successPeople->toArray());
        if (!empty($this->enteredRoles)) {
            $SuccessRoles = SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'Role')->pluck('name')->toArray();
            // dd($SuccessRoles);
            $NewRoleScore = SuccessPeople::where('plan_id', $this->plan->id)->whereIn('latest_role', $SuccessRoles)
                ->where('role_match', '!=', 1)
                ->get();
            $NoRoleScore = SuccessPeople::where('plan_id', $this->plan->id)->whereIn('latest_role', $SuccessRoles)
                ->where('role_match', '=', 1)
                ->get();

            foreach ($NewRoleScore as $ns) {
                $ns->update([
                    'role_match' => 1,
                    'total_match' => $ns->total_match + 1
                ]);
            }

            foreach ($NoRoleScore as $nr) {
                $nr->update([
                    'role_match' => 1,
                    'total_match' => $nr->total_match - 1
                ]);
            }
        }

        // Update the scoring for gender matches
        if (!empty($this->gender)) {
            $SuccessGender = SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'Gender')->pluck('name');
            // dd($SuccessGender);
            $NewGenderScore = $successPeople->whereIn('gender', $SuccessGender)
                ->where('gender_match', '!=', 1)
                ->get();
            $NoGenderScore = $successPeople->whereNotIn('gender', $SuccessGender)
                ->where('gender_match', '=', 1)
                ->get();

            foreach ($NewGenderScore as $gs) {
                $gs->update([
                    'gender_match' => 1,
                    'total_match' => $gs->total_match + 1
                ]);
            }

            foreach ($NoGenderScore as $ngs) {
                $ngs->update([
                    'gender_match' => 0,
                    'total_match' => $ngs->total_match - 1
                ]);
            }
        }

        //Update the scoring for location matches
        if (!empty($this->selectedCountries)) {
            $SuccessLocations = SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'location')->pluck('name');
            $NewLocScore = $successPeople->whereIn('location', $SuccessLocations)->get();
            $NoLocScore = $successPeople->whereNotIn('location', $SuccessLocations)->get();
            foreach ($NewLocScore as $ls) {
                $ls->update([
                    'location_match' => 1,
                    'total_score' => $ls->total_score + 1
                ]);
            }
            foreach ($NoLocScore as $nls) {
                if ($nls->location_match != 0) {
                    $nls->update([
                        'location_match' => 0,
                        'total_score' => $nls->total_score - 1
                    ]);
                }
            }
        }

        //Update the scoring for skills
        if (!empty($this->newSkills)) {
            // dd($this->updateskills);
            $SuccessSkills = SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'professional_skill')->pluck('name');
            $SuccessSkillsCount = SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'professional_skill')->groupBy('type')->count();
            $successPeoples = SuccessPeople::where('plan_id', $this->plan->id)->pluck('id')->toArray();
            // dd($successPeoples);
            $SkillMap = Skills::whereIn('people_id', $successPeoples)
                ->whereIn('skill_name', $this->newSkills)
                ->groupBy('people_id')
                ->selectRaw('COUNT(*) as skill_count')
                ->get();

            foreach ($SkillMap as $skillCount) {
                $successPeopleRecord = $successPeople->find($skillCount->people_id);

                if ($successPeopleRecord) {
                    $successPeopleRecord->update([
                        'skills_match' => $skillCount->skill_count / $SuccessSkillsCount,
                    ]);
                }
            }

            $InternalSkillMap = Skills::whereIn('people_id', $successPeoples)
                ->whereIn('skill_name', $this->newSkills)
                ->groupBy('people_id')
                ->selectRaw('COUNT(*) as skill_count')
                ->get();

            foreach ($InternalSkillMap as $skillCount) {
                $successPeopleRecord = $successPeople->find($skillCount->people_id);

                if ($successPeopleRecord) {
                    $successPeopleRecord->update([
                        'skills_match' => $skillCount->skill_count / $SuccessSkillsCount,
                    ]);
                }
            }
        }


        //------------------------ Make the pipeline table -------------------//

        //---- Get the data ready for the scoring ---//

        // Roles list
        $proles = SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'Role')->pluck('name')->toArray();

        // Division list
        $pDivs = SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'division')->pluck('name')->toArray();

        // Function list
        $pFuncs = SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'function')->pluck('name')->toArray();

        // Step-up List
        $psteps = SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'step_up')->pluck('name')->toArray();

                // Step-up List
        $pKeywords = SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'keyword')->pluck('name')->toArray();

        // Company list
        $pComp = SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'company')->pluck('name')->toArray();

        // Qualifcications list
        $pEdQual  = SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'education')->pluck('name')->toArray();

        // Location list
        $ploc  = SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'location')->pluck('name')->toArray();

        // Skills List
        $pskills  = SuccessRequirements::where('plan_id', $this->plan->id)->where('type', 'professional_skill')->pluck('name')->toArray();
        $skillCount = count($pskills);

        //See if a gender was chosen
        if ($this->gender == 'Female') {
            $gen = 'Female';
        } elseif ($this->gender == 'Male') {
            $gen = 'Male';
        } else {
            $gen = '';
        }

        //--------------- Start the filtering for the pipeline --------------//
        $scountry = $this->selectedCountries;
        //$scities   = $this->selectedCities;

        $filteredCompanyIdsArr = [];
        // if (!empty($this->selectedSectors) && !empty($this->selectedIndustries)) {
        //     $filteredCompanyIdsArr = Company::whereIn('industry', $this->selectedIndustries)
        //                                         ->whereIn('sector', $this->selectedSectors)
        //                                         ->pluck('id')
        //                                         ->toArray();
        // }
        // else if (!empty($this->selectedSectors)) {
        //     $filteredCompanyIdsArr = Company::whereIn('sector', $this->selectedSectors)
        //                                     ->pluck('id')
        //                                     ->toArray();
        // }
        // else if (!empty($this->selectedIndustries)) {
        if (!empty($this->selectedIndustries)) {
            $filteredCompanyIdsArr = Company::whereIn('industry', $this->selectedIndustries)
                                                ->pluck('id')
                                                ->toArray();
        }

        // This will filter the People table where the roles, gender, company_name, country and city 
        $filteredPeople = People::query()
            ->where('status', '!=', 'Submitted')
            ->whereNotIn('id', $peopleIdsArr)
            ->where('company_id','!=',$user->company_id)
            ->where('status', '!=', 'Reported')
            ->where(function ($query) use ($proles, $gen, $scountry, $filteredCompanyIdsArr) {
                $query->whereIn('latest_role', $proles);
                // The statement below will filter by the company_name
                if (!empty($this->selectedCompanies)) {
                    $query->WhereIn('company_name', $this->selectedCompanies);
                }
                else if(count($filteredCompanyIdsArr) > 0){
                    $query->WhereIn('company_id', $filteredCompanyIdsArr);
                }
                if (!empty($this->interestedCompaniesarray)){
                    $query->WhereIn('company_id',$this->interestedCompaniesarray);
                }
                if ($gen != '') {
                    $query->where('gender', $gen);
                }
                if (!empty($scountry)) {
                    $query->WhereIn('country', $scountry);
                }
                //if (!empty($scities)) {$query->WhereIn('city', $scities);}
            })
            ->get();

        $filteredPeople = $filteredPeople->map(function ($item) {
            $item['role_score'] = 1;
            return $item;
        });

        //dd($filteredPeople);

        $filteredPeopleidslvl1 = $filteredPeople->pluck('id');

        /* If a step up candidate has been proposed the system will only search for step candidates as well ensuring that
           people already identified from roles do not come through and uses the same filtering logic as roles
        */
        if (!empty($this->stepup)) {
            $sfilteredPeople = People::query()
                ->where('status', '!=', 'Submitted')
                ->where('status', '!=', 'Reported')
                ->where('company_id','!=',$user->company_id)
                ->whereNotIn('id', $peopleIdsArr)
                ->whereNotIn('id', $filteredPeopleidslvl1)
                ->when(!empty($this->stepup), function ($query) use ($psteps, $gen, $scountry, $filteredCompanyIdsArr) {
                    $query->whereIn('latest_role', $psteps);
                    if ($gen != '') {
                        $query->where('gender', $gen);
                    }
                    if (!empty($this->selectedCompanies)) {
                        $query->WhereIn('company_name', $this->selectedCompanies);
                    }
                    else if(count($filteredCompanyIdsArr) > 0){
                        $query->WhereIn('company_id', $filteredCompanyIdsArr);
                    }
                    if (!empty($this->interestedCompaniesarray)){
                        $query->WhereIn('company_id',$this->interestedCompaniesarray);
                    }
                    if (!empty($scountry)) {
                        $query->WhereIn('country', $scountry);
                    }
                })
                ->get();

            $sfilteredPeople = $sfilteredPeople->map(function ($item) {
                $item['role_score'] = 0.75;
                return $item;
            });
            // dd($this->pipelinepeople);

            //dd($sfilteredPeople);

            $sfilteredPeopleidslvl1 = $sfilteredPeople->pluck('id');
        } else {
            $sfilteredPeople = [];
            $sfilteredPeopleidslvl1 = [];
        }

        if(!empty($pKeywords)){
            $kfilteredPeople = People::query()
                ->where('status', '!=', 'Submitted')
                ->where('status', '!=', 'Reported')
                ->where('company_id','!=',$user->company_id)
                ->whereNotIn('id', $peopleIdsArr)
                ->whereNotIn('id', $filteredPeopleidslvl1)
                ->whereNotIn('id', $sfilteredPeopleidslvl1)
                ->when(!empty($pKeywords), function ($query) use ($pKeywords, $gen, $scountry, $filteredCompanyIdsArr) {
                    $query->where(function ($subquery) use ($pKeywords) {
                        // Loop through each keyword and add an orWhere clause
                        foreach ($pKeywords as $keyword) {
                            $subquery->orWhere('latest_role', 'like', '%' . $keyword . '%');
                        }
                    });

                    if ($gen != '') {
                        $query->where('gender', $gen);
                    }
                    if (!empty($this->selectedCompanies)) {
                        $query->whereIn('company_name', $this->selectedCompanies);
                    }
                    else if(count($filteredCompanyIdsArr) > 0){
                        $query->WhereIn('company_id', $filteredCompanyIdsArr);
                    }
                    if (!empty($this->interestedCompaniesarray)){
                        $query->WhereIn('company_id',$this->interestedCompaniesarray);
                    }
                    if (!empty($scountry)) {
                        $query->whereIn('country', $scountry);
                    }
                })
                ->get();
            $kfilteredPeople = $kfilteredPeople->map(function($item){
                $item['role_score'] = 0.25;
                return $item;
            });

            //dd($sfilteredPeople);
        
            $kfilteredPeopleidslvl1 = $kfilteredPeople->pluck('id');
        }
        else {
            $kfilteredPeople = [];
            $kfilteredPeopleidslvl1 =[];
        }

        // May be relevant candidates
        $career_history_filtered = CareerHistories::query()
            ->whereNotIn('people_id', $filteredPeopleidslvl1)
            ->whereNotIn('people_id', $peopleIdsArr)
            ->whereNotIn('people_id', $sfilteredPeopleidslvl1)
            ->whereNotIn('people_id',$kfilteredPeopleidslvl1)
            ->where(function ($query) use ($proles, $psteps) {
                $query->WhereIn('role', $proles)
                    ->orWhereIn('role', $psteps);
            })
            ->get();

        // First, get the relevant IDs from $career_history_filtered
        $filteredCareerHistoryIds = $career_history_filtered->pluck('people_id');

        if ($filteredCareerHistoryIds !== null) {
            // Then, use these IDs to filter the people table
            $careerPeople = People::query()
                ->whereIn('id', $filteredCareerHistoryIds)
                ->where('company_id','!=',$user->company_id)
                ->when($filteredCareerHistoryIds  !== null, function ($query) use ($gen, $scountry, $filteredCompanyIdsArr) {
                    if ($gen != '') {
                        $query->where('gender', $gen);
                    }
                    if (!empty($scountry)) {
                        $query->WhereIn('country', $scountry);
                    }
                    if (!empty($this->interestedCompaniesarray)){
                        $query->WhereIn('company_id',$this->interestedCompaniesarray);
                    }
                    if (!empty($this->selectedCompanies)) {
                        $query->WhereIn('company_name', $this->selectedCompanies);
                    }
                    else if(count($filteredCompanyIdsArr) > 0){
                        $query->WhereIn('company_id', $filteredCompanyIdsArr);
                    }
                })
                ->get();

            $careerPeople = $careerPeople->map(function ($item) {
                $item['role_score'] = 0.5;
                return $item;
            });

            $filteredPeople = $filteredPeople->concat($sfilteredPeople);
            $filteredPeople = $filteredPeople->concat($careerPeople);
            $filteredPeople = $filteredPeople->concat($kfilteredPeople);
        } else {
            $filteredPeople = $filteredPeople->concat($sfilteredPeople);
        }

        //dd($filteredPeople);

        //------------------------ Calculate Education Score  ------------------//

        // Query to join a filtered People table with the education requirements and summarise to get the count
        $educationCounts = People::query()
            ->whereIn('people.educational_history', $pEdQual)
            ->join('people as filtered_people', 'people.id', '=', 'filtered_people.id')
            ->groupBy('people.id')
            ->selectRaw('people.id, COUNT(filtered_people.id) as education_match_count')
            ->get();

        // Create a map of education match counts indexed by person ID
        $educationMatchCountMap = $educationCounts->pluck('education_match_count', 'id');

        // Left join to add the education_match_count column to filtered_people records
        $filteredPeopleWithEducationCount = $filteredPeople->map(function ($person) use ($educationMatchCountMap) {
            $person->education_match_count = $educationMatchCountMap->get($person->id, 0);
            return $person;
        });


        //------------------------- Calculate Location Score ---------------------//

        // Query to join the new filtered education People table with the loccation
        $locationCounts = People::query()
            ->whereIn('people.country', $ploc)
            ->join('people as filtered_people', 'people.id', '=', 'filtered_people.id')
            ->groupBy('people.id')
            ->selectRaw('people.id, COUNT(filtered_people.id) as location_match_count')
            ->get();

        // Create a map of location match counts indexed by person ID
        $locationMatchCountMap = $locationCounts->pluck('location_match_count', 'id');

        // Join the location match counts with the filteredPeopleWithEducationCount collection
        $filteredPeopleWithEdLocMatches = $filteredPeopleWithEducationCount->map(function ($person) use ($locationMatchCountMap) {
            $person->location_match_count = $locationMatchCountMap->get($person->id, 0);
            return $person;
        });

        //------------------------------ Calculate Skill Score -----------------------------//


        // Query to join a filtered people table with the skill requirements and summarise to 
        $skillsScoreCount = Skills::query()
            ->whereIn('skill_name', $pskills)
            ->groupBy('people_id')
            ->selectRaw('skills.people_id, COUNT(skills.people_id) / ? as skill_score', ['skillCount' => $skillCount])
            ->get();

        // Create a map of skill match score indexed by the person ID
        $skillsScoreMap = $skillsScoreCount->pluck('skill_score', 'people_id');

        // Join the skill score map with the filteredPeoplewithEdLocMatches collection
        $filteredPeopleWithMatches = $filteredPeopleWithEdLocMatches->map(function ($person) use ($skillsScoreMap) {
            $person->skill_score = $skillsScoreMap->get($person->id, 0);
            return $person;
        });

        //--------------------------------------- Gender ------------------------------------//

        // Calculate and assign gender score
        $filteredPeopleWithMatches->each(function ($person, $gen) {
            $person->gender_score = ($person->gender === $gen) ? 1 : 0;
        });

        // Calculate and assign tenancy score
        if ($this->minExp && isset($this->minExp[0])) {
            $filteredPeopleWithMatches = $filteredPeopleWithMatches->map(function ($person) {
                $tenancyDifference = $person->tenure - $this->minExp[0];
                $tenancyDifferenceAbsolute = abs($tenancyDifference);

                if ($tenancyDifference < 0) {
                    if ($tenancyDifferenceAbsolute <= 2) {
                        $person->tenancy_score = 0;
                    } else {
                        $person->tenancy_score = 1;
                    }
                } else {
                    $person->tenancy_score = 1;
                }
                return $person;
            });
        }


        //------------------------------  Make all the tables --------------------------------//

        // Put that data into the successionpeople list
        $filteredPeopleWithMatches->each(function ($person) use ($plan, $user) {
            pipeline::create([
                'plan_id'            => $plan->id,
                'user_id'            => $user->id,
                'people_id'          => $person->id,
                'first_name'         => $person->forename,
                'last_name'          => $person->surname,
                'middle_name'        => $person->middle_name,
                'other_name'         => $person->other_name,
                'gender'             => $person->gender,
                'diverse'            => $person->diverse,
                'location'           => $person->location,
                'summary'            => $person->summary,
                'country'            => $person->country,
                'city'               => $person->city,
                'linkedinURL'        => $person->linkedinURL,
                'latest_role'        => $person->latest_role,
                'company_id'         => $person->company_id,
                'company_name'       => $person->company_name,
                'start_date'         => $person->start_date,
                'end_date'           => $person->end_date,
                'tenure'             => $person->tenure,
                'function'           => $person->function,
                'division'           => $person->division,
                'seniority'          => $person->seniority,
                'readiness'          => $person->readiness,
                'other_tags'         => $person->other_tags,
                'exco'               => $person->exco,
                'career_history'     => $person->career_history,
                'educational_history' => $person->educational_history,
                'skills'             => $person->skills,
                'languages'          => $person->languages,
                'skills_match'       => $person->skill_score,
                'education_match'    => $person->education_match_count,
                'location_match'     => $person->location_match_count,
                'role_match'         => $person->role_score,
                'gender_match'       => $person->gender_score,
                'tenure_match'       => $person->tenancy_score,
                'total_score'        => $person->skill_score + $person->education_match_count + $person->location_match_count + $person->role_score + $person->tenancy_score,
                'people_type'        => 'External-System'
            ]);
        });


        // $this->reset('plan_name', 'description', 'status', 'minExp','selectedCountries','selectedcontributers');
        $this->newSkillData = [
            'qualifications'  => [],
            'skills'          => [],
            'targetRoles'     => [],
            'stepUpCandidate' => [],
            'keyword'         => []
        ];
        $this->step++;
    }



    public function clearModel()
    {
        $this->name = ''; // Clear the 'name' property
        $this->type = '';
    }

    public function deleteRequirement($id)
    {
        $requirement = SuccessRequirements::findOrFail($id);
        $requirement->delete();
    }

    public function deleteUserNote($id)
    {
        $userNote = UserNotes::findOrFail($id);
        $userNote->delete();
    }

    // Dealing with Success People
    public function removeSuccessPerson($id)
    {
        $removalcandidate = SuccessPeople::findOrFail($id);

        $removalcandidate->delete();

        // Update the scores
        //------------------- Get the new Gender Diversity Score -----------------//
        $femaleRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(gender = "Female")/Count(*))*100 as female_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan->id)
            ->first();

        if ($femaleRatio === null) {
            PlanScores::updateOrInsert(
                ['succession_plan_id' => $this->plan->id, 'metric_name' => 'Female-ratio'],
                ['score' => 0]
            );
        } else {
            PlanScores::updateOrInsert(
                ['succession_plan_id' => $this->plan->id, 'metric_name' => 'Female-ratio'],
                ['score' => $femaleRatio->female_ratio]
            );
        };


        $maleRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(gender = "male")/Count(*))*100 as male_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan->id)
            ->first();

        if ($maleRatio === null) {
            PlanScores::updateOrInsert(
                ['succession_plan_id' => $this->plan->id, 'metric_name' => 'Male-Ratio'],
                ['score' => 0]
            );
        } else {
            PlanScores::updateOrInsert(
                ['succession_plan_id' => $this->plan->id, 'metric_name' => 'Male-Ratio'],
                ['score' => $maleRatio->male_ratio]
            );
        }

        $InternalRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(type = "Internal")/Count(*))*100 as internal_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan->id)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan->id, 'metric_name' => 'Internal-External Ratio'],
            ['score' => $InternalRatio->internal_ratio ?? 0]
        );

        $averageskillscore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(skills_match)*100 as average_skill_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan->id)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan->id, 'metric_name' => 'Skill Score'],
            ['score' => $averageskillscore->average_skill_score ?? 0]
        );

        $averagetenurescore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(tenure_match)*100 as average_tenure_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan->id)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan->id, 'metric_name' => 'Tenure Score'],
            ['score' => $averagetenurescore->average_tenure_score ?? 0]
        );

        $this->successpeople = SuccessPeople::where('plan_id', $this->plan->id)
            ->orderBy('total_score', 'desc')
            ->get();

        $this->successArray = $this->successpeople->pluck('people_id')->toArray();

        $this->pipelinepeople = DB::table('pipelines')
                                ->where('plan_id', $this->plan->id)
                                ->whereNotIn('people_id', $this->successArray)
                                ->take(10)
                                ->get();
    }

    public function viewSelectedPeople($id, $peopleId, $type)
    {
        $this->savedPeople = $id;

        if(strtolower($type) == "internal") {
            $this->peoplescareer = internal_career_histories::where('people_id', $peopleId)
            ->join('companies', 'internal_career_histories.past_company_id', '=', 'companies.id')
            ->select('internal_career_histories.*', 'companies.name as company_name')
            ->orderBy('internal_career_histories.start_date', 'desc')
            ->get();           

        } else {
         // Get the career histories of the individual
         $this->peoplescareer = CareerHistories::where('people_id', $peopleId)
         ->join('companies', 'career_histories.past_company_id', '=', 'companies.id')
         ->select('career_histories.*', 'companies.name as company_name')
         ->orderBy('career_histories.start_date', 'desc')
         ->get();
        }
        
        // Load skills data for the selected person
        $skills_list = Skills::where('people_id', $peopleId)->get();
        $filteredPeople = SuccessPeople::where('id', $id)->get();
        if ($filteredPeople->isNotEmpty()) {
            $filteredPeople->each(function ($person) use ($skills_list) {
                $person->skills_list = $skills_list ? $skills_list->toArray() : null;
            });
        }
    }

    public function approveSuccessPerson($id)
    {
        $successPerson = SuccessPeople::findOrFail($id);
        $user = auth()->user();

        if ($successPerson->company_id === $user->company_id) {
            $successPerson->update(['status' => 'Approved']);
        } else {
            $successPerson->update(['status' => 'Approved']);
        }

        $proposedpeople = SuccessPeople::where('plan_id', $this->plan->id)
            ->where('status', '=', 'Proposed');


        if ($proposedpeople->count() == 0) {
            $plan = SuccessionPlan::find($this->plan->id);
            $plan->update([
                'candidate_status'          => 'Approved',
            ]);
        }

        $this->successpeople = SuccessPeople::where('plan_id', $this->plan->id)
            ->orderBy('total_score', 'desc')
            ->get();
    }

    public function addtoJob($id)
    {
        $user = auth()->user();
    }

    public function addNotes($id)
    {
        // $successPerson = SuccessPeople::findOrFail($id);
        // $successPerson->update(['notes' => $this->filteredPnotes]);

        $user = auth()->user();
        $userNoteData = [
            'entity_id' => $id,
            'entity_type' => 'success_person',
            'Notes' => $this->filteredPnotes,
            'author' => $user->id
        ];
        UserNotes::insert($userNoteData);
    }

    public function updateSummary($id)
    {
        $successPerson = SuccessPeople::findOrFail($id);
        $successPerson->update(['summary' => $this->successPersonSummary]);
    }

    public function updateScore($id)
    {

        $updateSuccessor = SuccessPeople::where('id', $id);
        $redSuc = SuccessPeople::where('id', $id)->first();

        $this->updategenScore = $this->updategenScore ?? $redSuc->gender_match;
        $this->updaterolScore = $this->updaterolScore ?? $redSuc->role_match;
        $this->updatetenureScore = $this->updatetenureScore ?? $redSuc->tenure_match;
        $this->updatelocScore = $this->updatelocScore ?? $redSuc->location_match;
        $this->updateskillScore = $this->updateskillScore ?? $redSuc->skills_match;

        $updateSuccessor->update([
            'gender_match' => $this->updategenScore,
            'role_match'   => $this->updaterolScore,
            'tenure_match' => $this->updatetenureScore,
            'location_match'   => $this->updatelocScore,
            'skills_match' => $this->updateskillScore,
            'total_score' => (+$this->updategenScore) + (+$this->updaterolScore) + (+$this->updatetenureScore) + (+$this->updatelocScore)  + (+$this->updateskillScore)
        ]);

        //
        $averageskillscore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(skills_match)*100 as average_skill_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan->id)
            ->first();

        //dd($averageskillscore);

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan->id, 'metric_name' => 'Skill Score'],
            ['score' => $averageskillscore->average_skill_score]
        );

        $averagetenurescore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(tenure_match)*100 as average_tenure_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $this->plan->id)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $this->plan->id, 'metric_name' => 'Tenure Score'],
            ['score' => $averagetenurescore->average_tenure_score]
        );
    }

    public function modalClosed()
    {
        $this->step = 1;
        $this->editPlanPopup = false;
    }

    public function addRoleTag()
    {
        if (!empty($this->roleTag)) {
            $this->enteredRoles[] = $this->roleTag;
            $this->roleTag = '';
        }
    }

    public function removeRoleTag($index)
    {
        unset($this->enteredRoles[$index]);
        $this->enteredRoles = array_values($this->enteredRoles); // Re-index the array
    }

    public function addSkillTag()
    {
        if (!empty($this->newSkillTag)) {
            $this->newSkills[] = $this->newSkillTag;
            $this->newSkillTag = '';
        }
    }

    public function removeSkillTag($index)
    {
        unset($this->newSkills[$index]);
        $this->newSkills = array_values($this->newSkills); // Re-index the array
    }

    public function addQualificationTag()
    {
        if (!empty($this->qualificationTag)) {
            $this->qualifications[] = $this->qualificationTag;
            $this->qualificationTag = '';
        }
    }

    public function removeQualificationTag($index)
    {
        unset($this->qualifications[$index]);
        $this->qualifications = array_values($this->qualifications); // Re-index the array
    }

    public function addStepupTag()
    {
        if (!empty($this->stepupTag)) {
            $this->stepup[] = $this->stepupTag;
            $this->stepupTag = '';
        }
    }

    public function removeStepupTag($index)
    {
        unset($this->stepup[$index]);
        $this->stepup = array_values($this->stepup); // Re-index the array
    }

    public function changeMinimumTenure($flag)
    {
        if ($flag == 'decrease') {
            $this->minimum_Experience = $this->minimum_Experience <= 1 ? null : ($this->minimum_Experience - 1);
        } else if ($flag == 'increase') {
            $this->minimum_Experience = !$this->minimum_Experience ? 1 : ($this->minimum_Experience + 1);
        }
    }

    public function viewDetailModalClosed()
    {
        $this->reset('savedPeople');
    }

    public function onSelectPlan($id)
    {
        // $this->plan->id = $id;
        $this->newSkillData = [
            'qualifications'  => SuccessRequirements::where(['plan_id' => $this->plan->id, 'type' => 'education'])->pluck('name')->toArray(),
            'skills'          => SuccessRequirements::where(['plan_id' => $this->plan->id, 'type' => 'professional_skill'])->pluck('name')->toArray(),
            'targetRoles'     => SuccessRequirements::where(['plan_id' => $this->plan->id, 'type' => 'Role'])->pluck('name')->toArray(),
            'stepUpCandidate' => SuccessRequirements::where(['plan_id' => $this->plan->id, 'type' => 'step_up'])->pluck('name')->toArray(),
            'keyword'         => SuccessRequirements::where(['plan_id' => $this->plan->id, 'type' => 'keyword'])->pluck('name')->toArray()
        ];

        //Getting the model inputs for the form
        $this->name = $this->plan->name;
        $this->descriptions = $this->plan->description;
        $this->selectedTaggedRole = $this->plan->tagged_individual;
        $this->selectedColleagues = $this->plan->shared_with ? json_decode($this->plan->shared_with) : [];
        $this->ethnicity = $this->plan->ethnicity;
        $this->status = $this->plan->status;
        $this->minimum_Experience = $this->plan->minimum_Experience;
        $this->selectedIndustries = [];
        $this->selectedIndustries = [];
        // $this->selectedSectors = [];
        
        $requirements = SuccessRequirements::where('plan_id', $this->plan->id)->get();

        if ($requirements->isNotEmpty()) {
            foreach ($requirements as $requirement) {
                if ($requirement->type == 'Role' && $requirement->name) {
                    $this->enteredRoles[] = $requirement->name;
                }

                if ($requirement->type == 'step_up' && $requirement->name) {
                    $this->stepup[] = $requirement->name;
                }

                if ($requirement->type == 'keyword' && $requirement->name) {
                    $this->keyword[] = $requirement->name;
                }

                if ($requirement->type == 'education' && $requirement->name) {
                    $this->qualifications[] = $requirement->name;
                }

                if ($requirement->type == 'professional_skill' && $requirement->name) {
                    $this->newSkills[] = $requirement->name;
                }

                if ($requirement->type == 'Minimum_Tenure' && $requirement->name) {
                    $this->minimum_Experience = $requirement->name;
                }

                if ($requirement->type == 'location' && $requirement->name) {
                    $this->selectedCountries[] = $requirement->name;
                }

                if ($requirement->type == 'Gender' && $requirement->name) {
                    $this->gender = $requirement->name;
                }

                if ($requirement->type == 'Company' && $requirement->name) {
                    $this->selectedCompanies[] = $requirement->name;
                }

                // if ($requirement->type == 'Sector' && $requirement->name) {
                //     $this->selectedSectors[] = $requirement->name;
                // }

                if ($requirement->type == 'Industry' && $requirement->name) {
                    $this->selectedIndustries[] = $requirement->name;
                }
            }
        }
        
        $this->editPlanPopup = true;
    }

    public function approveSuccessPeople($id) {

        $successPeople = SuccessPeople::find($id);

        if(!$successPeople)
            $this->dispatch('toast', 'error', 'Record not found');

        $successPeople->update(['status' => 'Approved']);
        $this->dispatch('toast', 'info', 'Status approved successfully!');

    } 
	

	public function SaveProject() {
		// dd();
		$userId = Auth::id();
		// if($this->recruitmentTitle){
			$recruitment = Recruitment::create([
				'user_id' => $userId,
				'recruitment_name' =>$this->plan->name,
				'shared_with' => '',
			]);
			$counter=0;
			foreach ($this->interviewfields as $stage) {
				$counter++;
				RecruitmentStage::create([
					'user_id' => $userId, 
					'recruitment_project_id' => $recruitment->id, 
					'stage_name' => $stage,
					'stage_number' => $counter,
				]);
			}
			// $this->selectedProject=$recruitment->id;
		// }
		$PlanexistingPipeline = RecruitmentPipeline::where('Recruitment_project_id', $recruitment->id)
								->whereNotNull('Plan_id')
								->where('Plan_id', '!=', '')
								->first();
	// dd($PlanexistingPipeline->Plan_id); die;
		if ($PlanexistingPipeline && $PlanexistingPipeline->Plan_id!=$this->plan->id) {
			$this->dispatch('toast', 'info', 'This Recruitment project has already been assigned to another plan.');
		}else{
			 // $jobId = JobPeople::where('people_id', $this->selectedpersonId)->value('job_id');
			$existingPipeline = RecruitmentPipeline::where('Candidate_ID', $this->selectedpersonId)
				->where('Recruitment_project_id', $recruitment->id)
				->where('Plan_id', $this->plan->id)
				->first();
             
                 
			if ($existingPipeline) {
				$this->dispatch('toast', 'info', 'Record already exists!');
			} else {
				RecruitmentPipeline::create([
					'Recruitment_project_id' => $recruitment->id,
					'Plan_id' => $this->plan->id,
					'Job_id' => 0, 
					'Candidate_ID' => $this->selectedpersonId,
					'Phone_number' => '',
					'Email' =>'',
					'Address' => '',
					'Link_cv' => '',
					'Status' =>'',
					'Consent_document' => '',
				]);
				
				$this->dispatch('project-saved');
				$this->dispatch('toast', 'success', 'Candidate has been added Successfully!');
			}
		}
	}
	
	public function updatedInterviewCount(){
		$this->interviewCount = max(1, (int)$this->interviewCount); // Ensure a minimum of 1
		$this->initializeFields();
	}

	private function initializeFields(){
		$this->interviewfields = array_map(fn($i) => $this->interviewfields[$i] ?? '', range(0, $this->interviewCount - 1));
	}

    public function increment(){
        $this->interviewCount++;
        $this->initializeFields();
    }

    public function decrement(){
        if ($this->interviewCount > 1) {
            $this->interviewCount--;
            $this->initializeFields();
        }
    }

    public function savePlanPeopleId($people_id,$recruitmentProjectId){
		$existingPipeline = RecruitmentPipeline::where('Candidate_ID', $people_id)
				->where('Recruitment_project_id', $recruitmentProjectId)
				->where('Plan_id', $this->plan->id)
				->first();
			if ($existingPipeline) {
				$this->dispatch('toast', 'info', 'Record already exists!');
			}else {
                //dd($people_id);
                if (People::where('id', $people_id)->exists()) {
                    RecruitmentPipeline::create([
                        'Recruitment_project_id' => $recruitmentProjectId,
                        'Plan_id' => $this->plan->id,
                        'Job_id' => 0, 
                        'Candidate_ID' => $people_id,
                        'Phone_number' => '',
                        'Email' =>'',
                        'Address' => '',
                        'Link_cv' => '',
                        'Status' =>'',
                        'Consent_document' => '',
                    ]);
                    $this->dispatch('project-saved');
                    $this->dispatch('toast', 'success', 'Candidate has been added Successfully!');
                }else{
                    $this->dispatch('toast', 'error', 'Candidate not found in people table');
                }
	
			}

    }

}
