<?php

namespace App\Services\AI;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class LLMInterceptor
{
    protected bool $enabled;
    protected bool $logToDatabase;
    protected bool $logToFile;
    protected array $sensitiveKeys = ['api_key', 'authorization', 'token'];
    
    public function __construct()
    {
        $this->enabled = config('ai.interceptor.enabled', true);
        $this->logToDatabase = config('ai.interceptor.log_to_database', true);
        $this->logToFile = config('ai.interceptor.log_to_file', true);
    }
    
    /**
     * Intercept and log an LLM API call
     */
    public function intercept(
        string $service,
        string $endpoint,
        array $request,
        $response,
        float $duration,
        ?string $error = null,
        array $metadata = []
    ): void {
        if (!$this->enabled) {
            return;
        }
        
        $interceptData = [
            'service' => $service,
            'endpoint' => $endpoint,
            'request' => $this->sanitizeRequest($request),
            'response' => $this->sanitizeResponse($response),
            'duration_ms' => round($duration * 1000, 2),
            'error' => $error,
            'metadata' => array_merge($metadata, [
                'user_id' => Auth::id(),
                'session_id' => session()->getId(),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]),
            'timestamp' => Carbon::now(),
        ];
        
        // Extract key information for easier querying
        $interceptData['model'] = $this->extractModel($service, $request);
        $interceptData['tokens_used'] = $this->extractTokenUsage($service, $response);
        $interceptData['cost'] = $this->calculateCost($service, $interceptData['model'], $interceptData['tokens_used']);
        
        if ($this->logToDatabase) {
            $this->logToDatabase($interceptData);
        }
        
        if ($this->logToFile) {
            $this->logToFile($interceptData);
        }
    }
    
    /**
     * Log intercepted data to database
     */
    protected function logToDatabase(array $data): void
    {
        try {
            DB::table('llm_call_logs')->insert([
                'service' => $data['service'],
                'endpoint' => $data['endpoint'],
                'model' => $data['model'],
                'request_payload' => json_encode($data['request']),
                'response_payload' => json_encode($data['response']),
                'duration_ms' => $data['duration_ms'],
                'tokens_used' => json_encode($data['tokens_used']),
                'cost' => $data['cost'],
                'error' => $data['error'],
                'metadata' => json_encode($data['metadata']),
                'user_id' => $data['metadata']['user_id'] ?? null,
                'session_id' => $data['metadata']['session_id'] ?? null,
                'created_at' => $data['timestamp'],
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to log LLM call to database', [
                'error' => $e->getMessage(),
                'service' => $data['service'],
            ]);
        }
    }
    
    /**
     * Log intercepted data to file
     */
    protected function logToFile(array $data): void
    {
        try {
            $logEntry = [
                'timestamp' => $data['timestamp']->toIso8601String(),
                'service' => $data['service'],
                'endpoint' => $data['endpoint'],
                'model' => $data['model'],
                'duration_ms' => $data['duration_ms'],
                'tokens' => $data['tokens_used'],
                'cost' => $data['cost'],
                'error' => $data['error'],
                'user_id' => $data['metadata']['user_id'] ?? null,
            ];
            
            // Log summary to standard log
            Log::channel('llm_calls')->info('LLM API Call', $logEntry);
            
            // Log full details to separate file if configured
            if (config('ai.interceptor.log_full_payloads', false)) {
                $fullLogPath = storage_path('logs/llm_calls_full/' . date('Y-m-d') . '.log');
                $fullLogDir = dirname($fullLogPath);
                
                if (!is_dir($fullLogDir)) {
                    mkdir($fullLogDir, 0755, true);
                }
                
                file_put_contents(
                    $fullLogPath,
                    json_encode($data, JSON_PRETTY_PRINT) . "\n\n",
                    FILE_APPEND | LOCK_EX
                );
            }
        } catch (\Exception $e) {
            Log::error('Failed to log LLM call to file', [
                'error' => $e->getMessage(),
            ]);
        }
    }
    
    /**
     * Remove sensitive information from request
     */
    protected function sanitizeRequest(array $request): array
    {
        $sanitized = $request;
        
        foreach ($this->sensitiveKeys as $key) {
            if (isset($sanitized[$key])) {
                $sanitized[$key] = '***REDACTED***';
            }
            
            // Check nested arrays
            array_walk_recursive($sanitized, function (&$value, $k) use ($key) {
                if (strcasecmp($k, $key) === 0) {
                    $value = '***REDACTED***';
                }
            });
        }
        
        return $sanitized;
    }
    
    /**
     * Sanitize response data
     */
    protected function sanitizeResponse($response): array
    {
        if (is_string($response)) {
            try {
                $response = json_decode($response, true);
            } catch (\Exception $e) {
                return ['raw_response' => substr($response, 0, 1000) . '...'];
            }
        }
        
        if (!is_array($response)) {
            return ['response' => $response];
        }
        
        return $response;
    }
    
    /**
     * Extract model name from request
     */
    protected function extractModel(string $service, array $request): ?string
    {
        switch ($service) {
            case 'anthropic':
                return $request['model'] ?? null;
            case 'openai':
                return $request['model'] ?? null;
            case 'exa':
                return 'exa-search';
            default:
                return null;
        }
    }
    
    /**
     * Extract token usage from response
     */
    protected function extractTokenUsage(string $service, $response): array
    {
        if (!is_array($response)) {
            return [];
        }
        
        switch ($service) {
            case 'anthropic':
                return [
                    'input_tokens' => $response['usage']['input_tokens'] ?? 0,
                    'output_tokens' => $response['usage']['output_tokens'] ?? 0,
                    'total_tokens' => ($response['usage']['input_tokens'] ?? 0) + ($response['usage']['output_tokens'] ?? 0),
                ];
            case 'openai':
                return [
                    'prompt_tokens' => $response['usage']['prompt_tokens'] ?? 0,
                    'completion_tokens' => $response['usage']['completion_tokens'] ?? 0,
                    'total_tokens' => $response['usage']['total_tokens'] ?? 0,
                ];
            case 'exa':
                return [
                    'search_results' => count($response['results'] ?? []),
                ];
            default:
                return [];
        }
    }
    
    /**
     * Calculate approximate cost based on token usage
     */
    protected function calculateCost(string $service, ?string $model, array $tokenUsage): ?float
    {
        if (empty($tokenUsage) || !$model) {
            return null;
        }
        
        // Cost per 1K tokens (approximate)
        $pricing = [
            'anthropic' => [
                'claude-3-opus-20240229' => ['input' => 0.015, 'output' => 0.075],
                'claude-3-sonnet-20240229' => ['input' => 0.003, 'output' => 0.015],
                'claude-3-haiku-20240307' => ['input' => 0.00025, 'output' => 0.00125],
            ],
            'openai' => [
                'gpt-4o' => ['input' => 0.005, 'output' => 0.015],
                'gpt-4o-mini' => ['input' => 0.00015, 'output' => 0.0006],
                'gpt-4-turbo' => ['input' => 0.01, 'output' => 0.03],
                'gpt-3.5-turbo' => ['input' => 0.0005, 'output' => 0.0015],
            ],
        ];
        
        $cost = 0;
        
        switch ($service) {
            case 'anthropic':
                if (isset($pricing['anthropic'][$model])) {
                    $cost = ($tokenUsage['input_tokens'] ?? 0) / 1000 * $pricing['anthropic'][$model]['input'];
                    $cost += ($tokenUsage['output_tokens'] ?? 0) / 1000 * $pricing['anthropic'][$model]['output'];
                }
                break;
            case 'openai':
                if (isset($pricing['openai'][$model])) {
                    $cost = ($tokenUsage['prompt_tokens'] ?? 0) / 1000 * $pricing['openai'][$model]['input'];
                    $cost += ($tokenUsage['completion_tokens'] ?? 0) / 1000 * $pricing['openai'][$model]['output'];
                }
                break;
        }
        
        return round($cost, 6);
    }
    
    /**
     * Get statistics for LLM calls
     */
    public function getStatistics(array $filters = []): array
    {
        $query = DB::table('llm_call_logs');
        
        if (!empty($filters['service'])) {
            $query->where('service', $filters['service']);
        }
        
        if (!empty($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }
        
        if (!empty($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }
        
        return [
            'total_calls' => $query->count(),
            'total_cost' => $query->sum('cost'),
            'avg_duration_ms' => $query->avg('duration_ms'),
            'error_count' => $query->whereNotNull('error')->count(),
            'by_service' => $query->select('service', DB::raw('COUNT(*) as count'), DB::raw('SUM(cost) as total_cost'))
                ->groupBy('service')
                ->get(),
            'by_model' => $query->select('model', DB::raw('COUNT(*) as count'), DB::raw('SUM(cost) as total_cost'))
                ->groupBy('model')
                ->get(),
        ];
    }
}