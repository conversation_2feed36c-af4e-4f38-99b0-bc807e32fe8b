<?php

namespace App\Services\AI;

use App\Models\People;
use App\Models\Company;
use App\Models\pipeline;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Services\AI\RateLimitedOpenAiService;
use App\Services\AI\RateLimitedExaService;

class ExternalPeopleSearch
{
    /**
     * API keys for making requests to external services
     */
    protected $exaApiKey;
    protected $openAiApiKey;
    protected $rateLimitedOpenAi;
    protected $rateLimitedExa;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->exaApiKey = config('ai.exa.api_key');
        $this->openAiApiKey = config('ai.openai.api_key');
        $this->rateLimitedOpenAi = new RateLimitedOpenAiService();
        $this->rateLimitedExa = new RateLimitedExaService();
    }

    /**
     * Main method to execute external candidate search
     *
     * @param array $planData The succession plan data
     * @param object $user The current user
     * @return array List of candidate profiles found
     */
    public function searchExternalCandidates($planData, $user)
    {
        try {

            // Determine the query to use
            $query = '';

            // If user provided a search query, use it directly
            if (isset($planData['search_query']) && !empty($planData['search_query'])) {
                $query = $planData['search_query'];
            } else {
                // Create a simple template query from plan data
                $roles = !empty($planData['target_roles']) ? implode(' OR ', $planData['target_roles']) : '';
                $companies = !empty($planData['companies']) && $planData['companies'] != ['none'] ?
                    implode(' OR ', $planData['companies']) : '';
                $countries = !empty($planData['country']) && $planData['country'] != ['none'] ?
                    implode(' OR ', $planData['country']) : '';

                // Build the query
                $query = "Find " . $roles;
                if (!empty($companies)) {
                    $query .= " at " . $companies;
                }
                if (!empty($countries)) {
                    $query .= " in " . $countries;
                }
                $query .= " LinkedIn";

            }

            // Filter plan data to include only relevant fields for the API call
            $relevantPlanData = $this->prepareRelevantPlanData($planData);

            // Convert to JSON
            $planRequirementsJson = json_encode($relevantPlanData, JSON_PRETTY_PRINT);

            // Execute the search with the query
            $response = $this->executeApiRequest($query, $planRequirementsJson);

            if (!$response) {
                return [];
            }

            // Process the response
            return $this->processCandidateProfiles($response, $planData, $user);
        } catch (\Exception $e) {
            Log::error("Exception in searchExternalCandidates", [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return empty array to avoid breaking the job
            return [];
        }
    }

    /**
     * Prepare relevant plan data for the API call
     *
     * @param array $planData Full plan data
     * @return array Filtered plan data
     */
    protected function prepareRelevantPlanData($planData)
    {
        return [
            'plan_name' => $planData['plan_name'] ?? '',
            'target_roles' => $planData['target_roles'] ?? [],
            'alternative_roles_titles' => $planData['alternative_roles_titles'] ?? [],
            'step_up_candidates' => $planData['step_up_candidates'] ?? [],
            'companies' => $planData['companies'] ?? [],
            'gender' => $planData['gender'] ?? 'Not required',
            'country' => $planData['country'] ?? [],
            'minimum_tenure' => $planData['minimum_tenure'] ?? null,
            'skills' => $planData['skills'] ?? [],
            'qualifications' => $planData['qualifications'] ?? [],
            'include_alumni' => $planData['include_alumni'] ?? null
        ];
    }

    /**
     * Execute search using provided query
     *
     * @param string $query Original search query from the plan
     * @param string $planRequirementsJson JSON string of plan requirements
     * @return array|null Response data or null on failure
     */
    protected function executeApiRequest($query, $planRequirementsJson)
    {
        try {
            // Parse the plan data
            $planData = json_decode($planRequirementsJson, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error("Failed to parse plan requirements JSON", [
                    'error' => json_last_error_msg(),
                    'json' => substr($planRequirementsJson, 0, 500) . '...'
                ]);
                return null;
            }

            Log::info("Using provided search query:", [
                'query' => $query
            ]);

            // Number of results to fetch - increased to ensure we get multiple profiles
            $numResults = 100;

            // Execute the search
            $searchResults = $this->performExaSearch($query, $numResults);
            if ($searchResults === null) {
                Log::error("Exa search failed");
                return null;
            }

            // Generate profiles using OpenAI with the search results
            $profiles = $this->generateProfilesFromResults($searchResults, $planData);

            Log::info("Successfully processed search results");
            return $profiles;

        } catch (\Exception $e) {
            Log::error("Exception in direct search process", [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Generate candidate profiles from search results using OpenAI
     *
     * @param array $searchResults The Exa search results
     * @param array $planData The plan requirements
     * @return array Formatted profiles
     */
    protected function generateProfilesFromResults($searchResults, $planData)
    {
        try {
            // If no search results, return a fallback
            if (empty($searchResults['results'])) {
                Log::error("No search results found in Exa response");
                return null;
            }

            $allProfiles = ['profiles' => []];

            // Process one candidate at a time
            foreach ($searchResults['results'] as $index => $result) {
                Log::info("Processing candidate " . ($index + 1) . " of " . count($searchResults['results']));

                // Create a single result array for this candidate
                $singleResult = [
                    'results' => [$result]
                ];

                // Create a prompt for one candidate only - modified to exclude score generation
                $prompt = "Generate a candidate profile from this LinkedIn search result. Create EXACTLY ONE profile.\n\n";
                $prompt .= "Search result:\n" . json_encode($singleResult, JSON_PRETTY_PRINT) . "\n\n";
                $prompt .= "Plan requirements:\n" . json_encode($planData, JSON_PRETTY_PRINT) . "\n\n";
                $prompt .= "CRITICAL INSTRUCTIONS:\n";
                $prompt .= "1. Create EXACTLY ONE profile for this result\n";
                $prompt .= "2. The profile must be based on the LinkedIn URL from the search result\n";
                $prompt .= "3. You MUST calculate match scores using these specific rules:\n";
                $prompt .= "\n";
                $prompt .= "   ROLE MATCH SCORING (role_match field):\n";
                $prompt .= "   - Score = 1.0 if the candidate's current role EXACTLY matches one of the target_roles with no additional prefixes/suffixes\n";
                $prompt .= "   - Score = 0.75 if the candidate's current role EXACTLY matches one of the step_up_candidates\n";
                $prompt .= "   - Score = 0.5 if any of the candidate's previous roles match target_roles (from career_history)\n";
                $prompt .= "   - Score = 0 if no match\n";
                $prompt .= "   - Only consider exact matches and these semantic equivalences: reversed title order (e.g., 'Director of X' = 'X Director') or standard abbreviations\n";
                $prompt .= "   - Standard abbreviations include: CEO=Chief Executive Officer, COO=Chief Operating Officer, CFO=Chief Financial Officer, CTO=Chief Technology Officer, CMO=Chief Marketing Officer, CHRO=Chief Human Resources Officer, CIO=Chief Information Officer, VP=Vice President, SVP=Senior Vice President, EVP=Executive Vice President, MD=Managing Director, GM=General Manager\n";
                $prompt .= "   - Roles with qualifiers (e.g., 'Area X', 'Regional X', 'Junior X', 'Senior X') should NOT match the base role unless explicitly listed\n";
                $prompt .= "   - IMPORTANT: Extract ONLY the clean role title from career history as latest_role with no company information or extra qualifiers\n";
                $prompt .= "\n";
                $prompt .= "   SKILLS MATCH SCORING (skills_match field):\n";
                $prompt .= "   - Count how many of the required skills match the candidate's skills\n";
                $prompt .= "   - Score = Number of matched skills / Total required skills\n";
                $prompt .= "   - Consider synonyms and related terms as matches\n";
                $prompt .= "\n";
                $prompt .= "   LOCATION MATCH SCORING (location_match field):\n";
                $prompt .= "   - Always set location_match = 1.0 (default value used in internal search)\n";
                $prompt .= "\n";
                $prompt .= "   GENDER MATCH SCORING (gender_match field):\n";
                $prompt .= "   - Score = 1.0 if gender matches preference \n";
                $prompt .= "   - Score = 0 if mismatch\n";
                $prompt .= "   - Score = 1.0 if no preference is specified\n";
                $prompt .= "\n";
                $prompt .= "   TENURE MATCH SCORING (tenure_match field):\n";
                $prompt .= "   - Score = 1.0 if candidate's tenure meets or exceeds minimum\n";
                $prompt .= "   - Score = 0 if candidate's tenure is within 2 years below minimum\n";
                $prompt .= "   - Score = 1.0 if candidate's tenure is more than 2 years below minimum\n";
                $prompt .= "   - Default to 1.0 if no minimum is specified\n";
                $prompt .= "\n";
                $prompt .= "   EDUCATION MATCH SCORING (education_match field):\n";
                $prompt .= "   - Count how many of the required qualifications match the candidate's education\n";
                $prompt .= "   - Set education_match = matched_count (not as a ratio, just the raw count)\n";
                $prompt .= "   - Consider degree equivalence (e.g., 'MBA' = 'Master of Business Administration')\n";
                $prompt .= "\n";
                $prompt .= "   TOTAL SCORE CALCULATION (total_score field):\n";
                $prompt .= "   - Simply add all scores: total_score = skills_match + education_match + location_match + role_match + tenure_match\n";
                $prompt .= "   - Gender match is not included in total score\n";
                $prompt .= "\n";
                $prompt .= "4. You MUST provide detailed match_reasoning explaining how you calculated each score component\n";
                $prompt .= "Create profile in this exact format (JSON object):\n";
                $prompt .= <<<EOT
{
  "profiles": [
    {
      "people_data": {
        "forename": "First name",
        "surname": "Last name",
        "gender": "Male/Female (use best guess based on first and last name)",
        "diverse": "Irish/Italian/Asian/German/English/Indian/French/Spanish/Other (infer likely ethnicity based on full name)",
        "country": "United Kingdom",
        "city": "City name",
        "linkedinURL": "LinkedIn profile URL",
        "latest_role": "ONLY the most recent/current job title (extract just the specific role title without the company name)",
        "company_name": "Current company",
        "start_date": "YYYY-MM-DD format if available",
        "end_date": null,
        "tenure": 5,
        "function": "Operations",
        "division": "Banking Operations",
        "seniority": "Head/Manager/Director",
        "exco": "Non Exco",
        "career_history": "EXTRACT THE FULL CAREER HISTORY, INCLUDING ALL POSITIONS. Format as chronological list with dates, titles, companies, and locations (e.g., 'Sep-2023 | Present: Client Account Manager at UBS, London\\nApr-2022 | Sep-2023: Private Banking Executive at Barclays Corporate & Investment Bank, London'). Include ALL positions from the profile, even older roles. If multiple positions at same company, list each separately with its own date range.",
        "educational_history": "Format as 'Institution, Field of Study - Degree Type · Graduation Year' (e.g., 'University of Birmingham, Chemistry - Bachelor of Science (BSc) · 1989'). Include multiple entries if available, each on a new line.",
        "skills": "Comma-separated list of skills",
        "languages": "English, plus any other languages",
        "summary": "Detailed professional summary (150-250 words) describing: 1) Area of expertise, 2) Years and depth of experience, 3) Types of organizations worked with, 4) Scale of responsibilities (team size, budget, geographic scope, etc.), 5) Key achievements, 6) Current status/availability if relevant. Example: 'HR leader with deep experience across HRBP, Reward/Compensation & Benefits and Talent roles, in both consulting and corporate contexts. Led large HR teams (>300 employees, $43m budget), covering country, regional and global roles (62 countries), and large businesses (>34,000 headcount). Currently providing interim reward consultancy advice, including through association with 3XO. Available for permanent roles at short notice (generalist or reward, in London, UK).'"
      },
      "pipeline_data": {
        "first_name": "Same as forename",
        "last_name": "Same as surname",
        "gender": "Same as people_data (Male/Female)",
        "diverse": "Same as people_data (ethnicity)",
        "country": "Same as above",
        "city": "Same as above",
        "location": "City only",
        "contact_number": "Candidate's phone number if available, otherwise leave blank",
        "email_address": "Candidate's email address if available, otherwise leave blank",
        "summary": "Same as people_data (detailed professional summary)",
        "linkedinURL": "Same as above",
        "latest_role": "Same as people_data (ONLY the most recent job title)",
        "company_name": "Same as above",
        "start_date": "Same as above",
        "end_date": null,
        "tenure": 5,
        "function": "Same as above",
        "division": "Same as above",
        "seniority": "Same as above",
        "exco": "Same as above",
        "career_history": "Same as people_data (complete formatted chronological history with ALL positions)",
        "educational_history": "Same as people_data (formatted education with institution, field, degree, and year)",
        "skills": "Same as above",
        "languages": "Same as above",
        "readiness": "Ready",
        "role_match": 0.5,
        "skills_match": 0.4,
        "location_match": 1.0,
        "gender_match": 1.0,
        "tenure_match": 1.0,
        "education_match": 0.3,
        "total_score": 3.2
      },
      "match_reasoning": "Detailed scoring breakdown: 1) ROLE MATCH: Explain how the candidate's role title compares to target roles, noting any semantic equivalences (e.g., 'Director of Sales' = 'Sales Director'). 2) SKILLS MATCH: List each required skill and whether candidate has it or an equivalent. 3) LOCATION MATCH: Explain location matching logic. 4) GENDER MATCH: Note gender match/mismatch. 5) TENURE MATCH: Calculate years in role vs minimum requirement. 6) EDUCATION MATCH: List required qualifications and any matches. 7) TOTAL SCORE: Show the weighted calculation. Include specific score values for each component."
    }
  ]
}
EOT;

                Log::info("Requesting profile generation for candidate " . ($index + 1), [
                    'model' => 'gpt-4o'
                ]);

                // Make API call to OpenAI
                $payload = [
                    'model' => 'gpt-4o',
                    'messages' => [
                        [
                            'role' => 'user',
                            'content' => $prompt
                        ]
                    ],
                    'temperature' => 0.2,
                    'response_format' => ['type' => 'json_object']
                ];

                // Use rate-limited OpenAI service
                $rateLimitKey = "external-search-" . ($planData['plan_id'] ?? 'unknown');
                $data = $this->rateLimitedOpenAi->chatCompletion($payload, $rateLimitKey);

                if (!$data) {
                    Log::error("OpenAI profile generation failed for candidate " . ($index + 1) . " - rate limited or API error");
                    continue; // Skip this candidate and try the next one
                }
                $content = $data['choices'][0]['message']['content'] ?? '[]';

                // Validate response
                $parsed = json_decode($content, true);
                if (json_last_error() !== JSON_ERROR_NONE || !is_array($parsed) || !isset($parsed['profiles'])) {
                    Log::error("Invalid JSON response from OpenAI for candidate " . ($index + 1), [
                        'error' => json_last_error_msg(),
                        'content' => substr($content, 0, 500) . '...'
                    ]);
                    continue; // Skip this candidate and try the next one
                }

                // Add the profile to our collection
                if (isset($parsed['profiles'][0])) {
                    $allProfiles['profiles'][] = $parsed['profiles'][0];
                    Log::info("Successfully added profile for candidate " . ($index + 1));
                }
            }

            // Log the total number of profiles generated
            $profilesCount = count($allProfiles['profiles']);
            Log::info("Total number of profiles generated: " . $profilesCount);

            // Calculate scores for each candidate
            $allProfiles = $this->calculateCandidateScores($allProfiles, $planData);

            // Format to match expected structure
            return [
                'output' => [
                    [
                        'content' => [
                            [
                                'text' => json_encode($allProfiles)
                            ]
                        ]
                    ]
                ]
            ];

        } catch (\Exception $e) {
            Log::error("Exception generating profiles", [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Perform the actual Exa search for LinkedIn profiles
     *
     * @param string $query The search query from the user
     * @param int $numResults Number of results to return
     * @return array|null Search results or null on failure
     */
    protected function performExaSearch($query, $numResults = 100)
    {
        try {
            Log::info("Executing Exa search", [
                'query' => $query,
                'numResults' => $numResults
            ]);

            // Updated payload with correct Exa API parameters
            $payload = [
                'query' => $query,
                'numResults' => $numResults,
                'type' => 'auto',  // Changed from 'neural' to 'auto' for better results
                'category' => 'linkedin profile',  // Use Exa's built-in category for LinkedIn profiles
                'contents' => [
                    'text' => true  // Simplified way to request text content
                ]
            ];

            // Log the exact query being sent to Exa
            Log::info("Exact payload being sent to Exa API:", [
                'endpoint' => 'https://api.exa.ai/search',
                'query' => $payload['query'],
                'numResults' => $payload['numResults'],
                'type' => $payload['type'],
                'category' => $payload['category'],
                'full_payload' => json_encode($payload)
            ]);

                        // Make the API call to Exa's search endpoint
                        $response = null;

                        try {
                            Log::info("Trying Exa search endpoint with rate limiting");
                            
                            // Use rate-limited Exa service
                            $rateLimitKey = "external-search-exa-" . ($query ? md5($query) : 'default');
                            $response = $this->rateLimitedExa->search($payload, $rateLimitKey);

                            if (!$response) {
                                Log::error("Exa search failed - rate limited or API error");
                                return null;
                            }

                            Log::info("Successful response from Exa search endpoint");
                        } catch (\Exception $e) {
                            $lastError = [
                                'exception' => $e->getMessage()
                            ];
                            Log::warning("Exception calling Exa search endpoint", $lastError);

                            // Return empty results on exception
                            Log::error("Exception in Exa search - returning empty results");
                            return null;
                        }

                        return $response;

                    } catch (\Exception $e) {
                        Log::error("Exception in Exa search", [
                            'message' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);

                        // Return empty results on error
                        Log::error("Exception in Exa search - returning empty results");
                        return null;
                    }
                }

                /**
                 * Process candidate profiles from the Exa Responses API response
                 * Handles deduplication, database storage, and pipeline integration
                 *
                 * @param array $responseData The response data from Exa Responses API
                 * @param array $planData The succession plan data
                 * @param object $user The current user
                 * @return array List of processed candidate profiles
                 */
                protected function processCandidateProfiles($responseData, $planData, $user)
                {
                    $candidates = [];
                    $duplicates = 0;

                    // Parse the OpenAI response (formatted to match old structure)
                    $content = $responseData['output'][0]['content'][0]['text'] ?? null;
                    if (empty($content)) {
                        Log::error("Empty or unexpected content in API response", [
                            'response' => json_encode($responseData)
                        ]);
                        return [];
                    }

                    try {
                        // Parse the JSON response
                        $processedData = json_decode($content, true);
                        if (json_last_error() !== JSON_ERROR_NONE) {
                            Log::error("Failed to parse JSON in API response", [
                                'error' => json_last_error_msg(),
                                'content' => substr($content, 0, 500) . '...'
                            ]);
                            return [];
                        }

                        // Extract profiles from the response object
                        $profiles = [];
                        if (isset($processedData['profiles']) && is_array($processedData['profiles'])) {
                            $profiles = $processedData['profiles'];
                        } else {
                            Log::warning("No profiles array found in response", [
                                'content' => substr($content, 0, 500) . '...'
                            ]);
                            return [];
                        }

                        // Log the number of profiles found
                        Log::info("Found " . count($profiles) . " profiles in the response");

                        foreach ($profiles as $profile) {
                            // Validate required fields
                            if (empty($profile['people_data']) || empty($profile['pipeline_data'])) {
                                Log::warning("Missing required data in profile", [
                                    'has_people_data' => !empty($profile['people_data']),
                                    'has_pipeline_data' => !empty($profile['pipeline_data'])
                                ]);
                                continue;
                            }

                            try {
                                // Extract key identifying information
                                $linkedinURL = $profile['people_data']['linkedinURL'] ?? '';
                                $forename = $profile['people_data']['forename'] ?? '';
                                $surname = $profile['people_data']['surname'] ?? '';
                                $company = $profile['people_data']['company_name'] ?? '';

                                // Check for duplicates by LinkedIn URL
                                $existingByURL = null;
                                if (!empty($linkedinURL)) {
                                    $existingByURL = People::where('linkedinURL', $linkedinURL)->first();
                                }

                                // If not found by URL, check by name and company (as fallback)
                                $existingByName = null;
                                if (!$existingByURL && !empty($forename) && !empty($surname) && !empty($company)) {
                                    $existingByName = People::where('forename', 'LIKE', $forename)
                                        ->where('surname', 'LIKE', $surname)
                                        ->where('company_name', 'LIKE', $company)
                                        ->first();
                                }

                                // If profile already exists, use that instead of creating a new one
                                if ($existingByURL || $existingByName) {
                                    $person = $existingByURL ?: $existingByName;
                                    $duplicates++;
                                    Log::info("Found duplicate profile", [
                                        'name' => $forename . ' ' . $surname,
                                        'company' => $company,
                                        'id' => $person->id
                                    ]);
                                } else {
                                    // Prepare and create new people record
                                    $personData = $profile['people_data'];
                                    $personData['user_id'] = $user->id;
                                    $personData['status'] = 'Submitted';
                                    $personData['readiness'] = 'Not Ready';

                                    // Remove contact fields from people data as they're not in the schema
                                    if (isset($personData['contact_number'])) {
                                        unset($personData['contact_number']);
                                    }

                                    if (isset($personData['email_address'])) {
                                        unset($personData['email_address']);
                                    }

                                    // Get company_id based on company_name or set to null if not found
                                    $company_name = $personData['company_name'] ?? '';
                                    $company = Company::where('name', 'LIKE', $company_name)->first();
                                    $personData['company_id'] = $company ? $company->id : null;

                                    // If company doesn't exist, create it
                                    if (!$company && !empty($company_name)) {
                                        try {
                                            $company = Company::create([
                                                'name' => $company_name,
                                                'status' => 'Active',
                                                // Add any other required fields for Company model
                                                'website' => null,
                                                'phone' => null,
                                                'logo' => null,
                                                'address' => null,
                                                'location_id' => 1, // Default location ID
                                                'company_size' => 'Unknown',
                                                'industry' => 'Unknown'
                                            ]);
                                            $personData['company_id'] = $company->id;
                                            Log::info("Created new company for profile", [
                                                'company_name' => $company_name,
                                                'company_id' => $company->id
                                            ]);
                                        } catch (\Exception $e) {
                                            Log::warning("Failed to create company, using null company_id", [
                                                'company_name' => $company_name,
                                                'error' => $e->getMessage()
                                            ]);
                                            // If company creation fails, we'll try to use a default company
                                            try {
                                                $defaultCompany = Company::first();
                                                if ($defaultCompany) {
                                                    $personData['company_id'] = $defaultCompany->id;
                                                    Log::info("Using default company instead", [
                                                        'default_company_id' => $defaultCompany->id
                                                    ]);
                                                }
                                            } catch (\Exception $e2) {
                                                Log::error("Failed to get default company", [
                                                    'error' => $e2->getMessage()
                                                ]);
                                            }
                                        }
                                    }

                                    $person = People::create($personData);
                                }

                                // Check if this person is already in the pipeline for this plan
                                $existingPipeline = pipeline::where('people_id', $person->id)
                                    ->where('plan_id', $planData['plan_id'])
                                    ->first();

                                if ($existingPipeline) {
                                    Log::info("Person already in pipeline for this plan", [
                                        'name' => $person->forename . ' ' . $person->surname,
                                        'plan_id' => $planData['plan_id']
                                    ]);
                                } else {
                                    // Prepare pipeline data
                                    $pipelineData = $profile['pipeline_data'];
                                    $pipelineData['plan_id'] = $planData['plan_id'];
                                    $pipelineData['user_id'] = $user->id;
                                    $pipelineData['people_id'] = $person->id;
                                    $pipelineData['people_type'] = 'External-AI';

                                    // Make sure all score fields are properly set
                                    $pipelineData['role_match'] = $profile['pipeline_data']['role_match'] ?? 0;
                                    $pipelineData['skills_match'] = $profile['pipeline_data']['skills_match'] ?? 0;
                                    $pipelineData['location_match'] = $profile['pipeline_data']['location_match'] ?? 0;
                                    $pipelineData['gender_match'] = $profile['pipeline_data']['gender_match'] ?? 0;
                                    $pipelineData['tenure_match'] = $profile['pipeline_data']['tenure_match'] ?? 0;
                                    $pipelineData['education_match'] = $profile['pipeline_data']['education_match'] ?? 0;
                                    $pipelineData['total_score'] = $profile['pipeline_data']['total_score'] ?? 0;

                                    // Verify role match scoring
                                    $targetRoles = $planData['target_roles'] ?? [];
                                    $stepUpCandidates = $planData['step_up_candidates'] ?? [];
                                    $candidateRole = trim($pipelineData['latest_role']);

                                    // Trust GPT's scoring without recalculation
                                    Log::info("Using GPT-calculated scores", [
                                        'name' => $person->forename . ' ' . $person->surname,
                                        'role' => $candidateRole,
                                        'role_match' => $pipelineData['role_match'],
                                        'skills_match' => $pipelineData['skills_match'],
                                        'location_match' => $pipelineData['location_match'],
                                        'tenure_match' => $pipelineData['tenure_match'],
                                        'education_match' => $pipelineData['education_match'],
                                        'total_score' => $pipelineData['total_score']
                                    ]);

                                    // Skip candidates with role_match of 0 (not relevant to target role)
                                    if ($pipelineData['role_match'] <= 0) {
                                        Log::info("Skipping candidate due to zero role match", [
                                            'name' => $person->forename . ' ' . $person->surname,
                                            'role' => $candidateRole,
                                            'role_match' => $pipelineData['role_match']
                                        ]);
                                        continue;
                                    }

                                    // Add company_id to pipeline data
                                    $pipelineData['company_id'] = $person->company_id;

                                    // Set contact info in pipeline data
                                    // If contact info isn't provided, default to empty string
                                    if (!isset($pipelineData['contact_number'])) {
                                        $pipelineData['contact_number'] = '';
                                    }

                                    if (!isset($pipelineData['email_address'])) {
                                        $pipelineData['email_address'] = '';
                                    }

                                    // Parse career history text and create entries in career_histories table
                                    $this->createCareerHistoryEntries($person->id, $person->career_history, $person->company_id);

                                    // Parse skills text and create entries in skills table
                                    $this->createSkillEntries($person->id, $person->skills);

                                    // Create pipeline record
                                    pipeline::create($pipelineData);

                                    // Add to candidates list
                                    $candidates[] = [
                                        'name' => $person->forename . ' ' . $person->surname,
                                        'role' => $pipelineData['latest_role'],
                                        'company' => $pipelineData['company_name'],
                                        'url' => $pipelineData['linkedinURL'],
                                        'score' => $profile['pipeline_data']['total_score'] ?? 0,
                                        'reasoning' => $profile['match_reasoning'] ?? ''
                                    ];

                                    Log::info("Added candidate to pipeline", [
                                        'name' => $person->forename . ' ' . $person->surname,
                                        'role' => $pipelineData['latest_role'],
                                        'score' => $pipelineData['total_score'] ?? 0
                                    ]);
                                }

                            } catch (\Exception $e) {
                                Log::error("Failed to process individual candidate profile", [
                                    'error' => $e->getMessage(),
                                    'trace' => $e->getTraceAsString()
                                ]);
                            }
                        }

                    } catch (\Exception $e) {
                        Log::error("Exception processing API response data", [
                            'message' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                    }

                    Log::info("EXTERNAL SEARCH: Completed with " . count($candidates) . " new candidates and " . $duplicates . " duplicates");
                    return $candidates;
                }

    /**
     * Collect statistics on scores for candidates
     *
     * @param array $profiles Candidate profiles
     * @param array $planData Plan requirements
     * @return array Profiles with scores from GPT
     */
    protected function calculateCandidateScores($profiles, $planData)
    {
        $profileCount = count($profiles['profiles']);
        Log::info("SCORING: Collecting statistics for {$profileCount} profiles");

        $scoreStats = [
            'max_score' => 0,
            'min_score' => 999,  // High initial value to ensure actual min is captured
            'avg_score' => 0,
            'total_scores' => 0
        ];

        foreach ($profiles['profiles'] as $index => &$profile) {
            $candidateName = $profile['people_data']['forename'] . ' ' . $profile['people_data']['surname'];
            
            // Get GPT-calculated scores
            $totalScore = isset($profile['pipeline_data']['total_score']) ?
                         floatval($profile['pipeline_data']['total_score']) : 0;

            // Update score statistics
            $scoreStats['total_scores'] += $totalScore;
            $scoreStats['max_score'] = max($scoreStats['max_score'], $totalScore);
            $scoreStats['min_score'] = min($scoreStats['min_score'], $totalScore);
            
            Log::info("SCORING: GPT scores for {$candidateName}: total={$totalScore}");
        }

        // Calculate average score
        if ($profileCount > 0) {
            $scoreStats['avg_score'] = round($scoreStats['total_scores'] / $profileCount, 2);
        }

        Log::info("SCORING: Statistics for all profiles", [
            'total_profiles' => $profileCount,
            'min_score' => $scoreStats['min_score'],
            'max_score' => $scoreStats['max_score'],
            'avg_score' => $scoreStats['avg_score']
        ]);

        return $profiles;
    }

    /**
     * Parse the career history text and create entries in the career_histories table
     *
     * @param int $peopleId The person's ID
     * @param string $careerHistoryText The raw career history text
     * @param int $currentCompanyId The ID of the current company (to avoid duplication)
     * @return void
     */
    protected function createCareerHistoryEntries($peopleId, $careerHistoryText, $currentCompanyId)
    {
        if (empty($careerHistoryText)) {
            Log::info("No career history text to parse for people_id: {$peopleId}");
            return;
        }

        Log::info("Creating career history entries for people_id: {$peopleId}");

        try {
            // Check if entries already exist for this person
            $existingEntries = \App\Models\CareerHistories::where('people_id', $peopleId)->count();
            if ($existingEntries > 0) {
                Log::info("Career history entries already exist for people_id: {$peopleId}, skipping creation");
                return;
            }

            // Split career history text into individual positions
            // Format example: "Sep-2023 | Present: Client Account Manager at UBS, London\nApr-2022 | Sep-2023: Private Banking Executive at Barclays Corporate & Investment Bank, London"
            $positions = explode("\n", $careerHistoryText);

            foreach ($positions as $position) {
                // Skip empty lines
                if (empty(trim($position))) {
                    continue;
                }

                // Parse the position string
                // First try to extract date range
                $dateParts = [];
                if (preg_match('/^(.*?)\s*\|\s*(.*?):\s*(.*)$/i', $position, $dateParts)) {
                    $startDateRaw = trim($dateParts[1]);
                    $endDateRaw = trim($dateParts[2]);
                    $roleAndCompany = trim($dateParts[3]);

                    // Parse role and company
                    $companyParts = [];
                    $role = $roleAndCompany;
                    $companyName = '';

                    if (preg_match('/(.*)\s+at\s+(.*)$/i', $roleAndCompany, $companyParts)) {
                        $role = trim($companyParts[1]);
                        $companyName = trim($companyParts[2]);

                        // Remove location if present (e.g., "Company, London")
                        if (strpos($companyName, ',') !== false) {
                            $companyName = trim(explode(',', $companyName)[0]);
                        }
                    }

                    // Find or create company
                    $company = \App\Models\Company::where('name', 'LIKE', $companyName)->first();
                    if (!$company && !empty($companyName)) {
                        try {
                            $company = \App\Models\Company::create([
                                'name' => $companyName,
                                'status' => 'Active',
                                'website' => null,
                                'phone' => null,
                                'logo' => null,
                                'address' => null,
                                'location_id' => 1, // Default location ID
                                'company_size' => 'Unknown',
                                'industry' => 'Unknown',
                                'sector' => 'Unknown'
                            ]);
                            Log::info("Created new company for career history: {$companyName}");
                        } catch (\Exception $e) {
                            Log::warning("Failed to create company for career history, using default: {$e->getMessage()}");
                            // Use current company as fallback
                            $company = \App\Models\Company::find($currentCompanyId);
                        }
                    }

                    // Skip if no company
                    if (!$company) {
                        Log::warning("No company found for career history entry, skipping");
                        continue;
                    }

                    // If this is the current position, skip it (already stored in people record)
                    if (strtolower($endDateRaw) === 'present' && $company->id === $currentCompanyId) {
                        Log::info("Skipping current position for people_id: {$peopleId} at company: {$companyName}");
                        continue;
                    }

                    // Convert date strings to proper format
                    $startDate = $this->parseCareerDate($startDateRaw);
                    $endDate = strtolower($endDateRaw) === 'present' ? now() : $this->parseCareerDate($endDateRaw);

                    // Calculate tenure
                    $tenure = 0;
                    if ($startDate && $endDate) {
                        $startDateTime = new \DateTime($startDate);
                        $endDateTime = new \DateTime($endDate);
                        $interval = $startDateTime->diff($endDateTime);
                        $tenure = $interval->y + ($interval->m / 12); // Years plus fraction of months
                    }

                    // Create career history entry
                    \App\Models\CareerHistories::create([
                        'people_id' => $peopleId,
                        'role' => $role,
                        'past_company_id' => $company->id,
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                        'tenure' => round($tenure, 1)
                    ]);

                    Log::info("Created career history entry: {$role} at {$companyName} for people_id: {$peopleId}");
                }
            }

            Log::info("Completed creating career history entries for people_id: {$peopleId}");

        } catch (\Exception $e) {
            Log::error("Error creating career history entries: {$e->getMessage()}", [
                'people_id' => $peopleId,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Parse the skills text and create entries in the skills table
     *
     * @param int $peopleId The person's ID
     * @param string $skillsText The raw skills text (comma-separated)
     * @return void
     */
    protected function createSkillEntries($peopleId, $skillsText)
    {
        if (empty($skillsText)) {
            Log::info("No skills text to parse for people_id: {$peopleId}");
            return;
        }

        Log::info("Creating skill entries for people_id: {$peopleId}");

        try {
            // Check if entries already exist for this person
            $existingEntries = \App\Models\Skills::where('people_id', $peopleId)->count();
            if ($existingEntries > 0) {
                Log::info("Skill entries already exist for people_id: {$peopleId}, skipping creation");
                return;
            }

            // Split skills string into individual skills
            $skills = array_map('trim', explode(',', $skillsText));

            // Create skill entries
            foreach ($skills as $skill) {
                if (empty($skill)) {
                    continue;
                }

                // Determine skill type based on keywords
                $skillType = $this->determineSkillType($skill);

                \App\Models\Skills::create([
                    'people_id' => $peopleId,
                    'skill_name' => $skill,
                    'skill_type' => $skillType
                ]);

                Log::info("Created skill entry: {$skill} ({$skillType}) for people_id: {$peopleId}");
            }

            Log::info("Completed creating skill entries for people_id: {$peopleId}");

        } catch (\Exception $e) {
            Log::error("Error creating skill entries: {$e->getMessage()}", [
                'people_id' => $peopleId,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Parse a date string from career history format (e.g. "Sep-2023")
     *
     * @param string $dateString The date string to parse
     * @return string|null The parsed date in Y-m-d format, or null if invalid
     */
    protected function parseCareerDate($dateString)
    {
        try {
            // Handle 'Present' case
            if (strtolower($dateString) === 'present') {
                return date('Y-m-d');
            }

            // Parse "MMM-YYYY" format (e.g., "Sep-2023")
            if (preg_match('/^([A-Za-z]{3,})-(\d{4})$/', $dateString, $matches)) {
                $month = $matches[1];
                $year = $matches[2];

                // Convert month name to number
                $dateObj = \DateTime::createFromFormat('M Y', substr($month, 0, 3) . ' ' . $year);
                if ($dateObj) {
                    return $dateObj->format('Y-m-d');
                }
            }

            // Try generic date parsing for other formats
            $date = \DateTime::createFromFormat('Y-m-d', $dateString);
            if (!$date) {
                $timestamp = strtotime($dateString);
                if ($timestamp === false) {
                    return null;
                }
                return date('Y-m-d', $timestamp);
            }

            return $date->format('Y-m-d');
        } catch (\Exception $e) {
            Log::warning("Failed to parse date string: {$dateString}");
            return null;
        }
    }

    /**
     * Determine the skill type based on the skill name
     *
     * @param string $skill The skill name
     * @return string The determined skill type
     */
    protected function determineSkillType($skill)
    {
        $skill = strtolower($skill);

        // Define keyword patterns for different skill types
        $technicalKeywords = [
            'programming', 'coding', 'software', 'database', 'sql', 'python', 'java', 'c++',
            'javascript', 'react', 'angular', 'vue', 'node', 'aws', 'azure', 'cloud',
            'devops', 'infrastructure', 'security', 'network', 'system', 'architecture',
            'engineering', 'technical', 'development', 'automation', 'testing'
        ];

        $leadershipKeywords = [
            'leadership', 'management', 'strategic', 'executive', 'director', 'c-level',
            'board', 'governance', 'vision', 'strategy', 'planning', 'transformation',
            'change management', 'organizational', 'team lead', 'mentoring', 'coaching'
        ];

        $communicationKeywords = [
            'communication', 'presentation', 'negotiation', 'public speaking', 'writing',
            'reporting', 'stakeholder', 'client', 'relationship', 'facilitation',
            'training', 'teaching', 'persuasion', 'influence', 'networking'
        ];

        $financialKeywords = [
            'financial', 'finance', 'accounting', 'budget', 'forecasting', 'investment',
            'revenue', 'profit', 'loss', 'balance sheet', 'p&l', 'tax', 'audit',
            'cost', 'pricing', 'valuation', 'funding', 'capital', 'banking', 'treasury'
        ];

        // Check for keyword matches in order of specificity
        foreach ($technicalKeywords as $keyword) {
            if (stripos($skill, $keyword) !== false) {
                return 'Technical';
            }
        }

        foreach ($leadershipKeywords as $keyword) {
            if (stripos($skill, $keyword) !== false) {
                return 'Leadership';
            }
        }

        foreach ($communicationKeywords as $keyword) {
            if (stripos($skill, $keyword) !== false) {
                return 'Communication';
            }
        }

        foreach ($financialKeywords as $keyword) {
            if (stripos($skill, $keyword) !== false) {
                return 'Financial';
            }
        }

        // Default skill type
        return 'Other';
    }
}