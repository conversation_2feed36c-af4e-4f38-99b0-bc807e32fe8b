<?php

use Illuminate\Support\Facades\Log;
use App\Models\People;
use App\Models\Job;
use App\Models\pipeline;
use App\Models\JobPeople;
use App\Models\SuccessionPlan;
use App\Models\SuccessPeople;
use App\Models\SuccessRequirements;
use App\Models\Skills;
use App\Models\PlanScores;
use App\Models\SuccessSkills;
use Illuminate\Support\Facades\DB;
use OpenAI\Laravel\Facades\OpenAI;






if (!function_exists('getPlansList')) {
function getPlansList($peopleId, $planId = null) {
    //dd($peopleId);
    $user = auth()->user();
    $sharedQuery = SuccessionPlan::where(function ($query) use ($user) {
        $query->where('user_id', $user->id)
              ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user->id]);
    });

    $planIds = SuccessPeople::where(['people_id' => $peopleId])
                            ->pluck('plan_id')
                            ->toArray();

    // Fetch plans excluding unwanted IDs
    $plansList = $sharedQuery->whereNotIn('id', $planIds)->get();

    // Rearrange only if planId is provided and valid
    if (!is_null($planId)) {
        $matchingPlan = $plansList->firstWhere('id', $planId);

        // If the matching plan exists, move it to the first position
        if ($matchingPlan) {
            $plansList = $plansList->reject(fn($plan) => $plan->id == $planId)
                                   ->prepend($matchingPlan);
        }
    }

    return $plansList;
}
}
if (!function_exists('getTalentPoolList')) {
function getTalentPoolList($peopleId, $jobId = null) {
    $jobIds = JobPeople::where('people_id', $peopleId)->pluck('job_id')->toArray();

    $user = auth()->user();
    $talentPoolsList = Job::where(function ($query) use ($user) {
        $query->where('user_id', $user->id)
              ->orWhereRaw("JSON_CONTAINS(shared_with, json_array(?))", [$user->id]);
    })
    ->whereNotIn('id', $jobIds)
    ->get();

    if (!is_null($jobId)) {
        $matchingTalentPool = $talentPoolsList->firstWhere('id', $jobId);

        // If the matching plan exists, move it to the first position
        if ($matchingTalentPool) {
            $talentPoolsList = $talentPoolsList->reject(fn($job) => $job->id == $jobId)
                                               ->prepend($matchingTalentPool);
        }
    }

    return $talentPoolsList;
}
}

if (!function_exists('addTojob')) 
{
    function addTojob($peopleId, $jobId)
    {
        //dd($peopleId);
        $user = auth()->user();
        $response = ['success' => false];

        $ExternalPerson = People::where('id', $peopleId)->first();
        $jobDetail = Job::find($jobId);

        if (!$jobDetail) {
            return $response['message'] = "Job not found!";
        }

        $pipelineData = [
            'job_id'            => $jobId,
            'user_id'            => $user->id,
            'people_id'          => $ExternalPerson->id,
            'first_name'         => $ExternalPerson->forename,
            'last_name'          => $ExternalPerson->surname,
            'middle_name'        => $ExternalPerson->middle_name,
            'other_name'         => $ExternalPerson->other_name,
            'gender'             => $ExternalPerson->gender,
            'diverse'            => $ExternalPerson->diverse,
            'location'           => $ExternalPerson->location,
            'linkedinURL'        => $ExternalPerson->linkedinURL,
            'latest_role'        => $ExternalPerson->latest_role,
            'company_id'         => $ExternalPerson->company_id,
            'company_name'       => $ExternalPerson->company_name,
            'start_date'         => $ExternalPerson->start_date,
            'end_date'           => $ExternalPerson->end_date,
            'tenure'             => $ExternalPerson->tenure,
            'function'           => $ExternalPerson->function,
            'division'           => $ExternalPerson->division,
            'seniority'          => $ExternalPerson->seniority,
            'career_history'     => $ExternalPerson->career_history,
            'educational_history' => $ExternalPerson->educational_history,
            'skills'             => $ExternalPerson->skills,
            'languages'          => $ExternalPerson->languages,
            'readiness'          => $ExternalPerson->readiness,
            'other_tags'         => $ExternalPerson->other_tags,
            'country'            => $ExternalPerson->country,
            'city'               => $ExternalPerson->city,
            'summary'            => $ExternalPerson->summary,
            'exco'               => $ExternalPerson->exco,
            'skills_match'       => 0,
            'education_match'    => 0,
            'location_match'     => 0,
            'role_match'         => 0,
            'gender_match'       => 0,
            'tenure_match'       => 0,
            'total_score'        => 0,
            'people_type'     => 'External-User',
        ];

        if ($jobDetail->user_id != auth()->user()->id) {
            $pipelineData['status'] = "Proposed";
        }

        $newPipeline = pipeline::create($pipelineData);

        $dataToCreate = [
            'pipeline_id'        => $newPipeline->id,
            'job_id'             => $jobId,
            'user_id'            => $user->id,
            'people_id'          => $ExternalPerson->id,
            'headline'           => 'Not Applicable',
            'first_name'         => $ExternalPerson->forename,
            'last_name'          => $ExternalPerson->surname,
            'middle_name'        => $ExternalPerson->middle_name,
            'other_name'         => $ExternalPerson->other_name,
            'gender'             => $ExternalPerson->gender,
            'diverse'            => $ExternalPerson->diverse,
            'location'           => $ExternalPerson->country,
            'linkedinURL'        => $ExternalPerson->linkedinURL,
            'latest_role'        => $ExternalPerson->latest_role,
            'company_id'         => $ExternalPerson->company_id,
            'company_name'       => $ExternalPerson->company_name,
            'start_date'         => $ExternalPerson->start_date,
            'end_date'           => $ExternalPerson->end_date,
            'tenure'             => $ExternalPerson->tenure,
            'function'           => $ExternalPerson->function,
            'division'           => $ExternalPerson->division,
            'seniority'          => $ExternalPerson->seniority,
            'career_history'     => $ExternalPerson->career_history,
            'educational_history' => $ExternalPerson->educational_history,
            'skills'             => $ExternalPerson->skills,
            'languages'          => $ExternalPerson->languages,
            'readiness'          => $ExternalPerson->readiness,
            'other_tags'         => $ExternalPerson->other_tags,
            'country'            => $ExternalPerson->country,
            'city'               => $ExternalPerson->city,
            'summary'            => $ExternalPerson->summary,
            'notes'              => "Enter notes here",
            'status'             => "Approved"
        ];

        if ($jobDetail->user_id != auth()->user()->id) {
            $dataToCreate['status'] = "Proposed";
        }

        $JobPeople = JobPeople::create($dataToCreate);

        return $response['success'] = true;
    }
}

if (!function_exists('addToPlan')) 
{
    function addToPlan($peopleId, $planId)
    {
        //dd($peopleId);
        $user = auth()->user();
        $ExternalPerson = People::where('id', $peopleId)->first();
        $successionPlan = SuccessionPlan::find($planId);

        $successRequirements = SuccessRequirements::where('plan_id', $planId)->get();

        // Group or filter the results based on the 'type'
        $SuccessSkills = $successRequirements->where('type', 'professional_skill');
        $SuccessRoles = $successRequirements->where('type', 'Role');
        $SuccessLocation = $successRequirements->where('type', 'Location');
        $SuccessGender = $successRequirements->where('type', 'Gender');
        $SuccessStepup = $successRequirements->where('type', 'step_up');


        $tenureMatch = 0;
        if ($ExternalPerson->tenure >= $successionPlan->minimum_Experience) {
            $tenureMatch = 1;
        }



        //Get the skills of the person this will be used for the radar chart
        $PersonSkills = Skills::where('people_id', $peopleId)->get();
        if ($ExternalPerson->addStartDate === null) {
            $ExternalPerson->start_date = now();
        };

        //Get there scores ready for the pipeline and plans
        //Role Scores
        $latestRole = $ExternalPerson->latest_role;

        $RoleScore = 0;
        if ($SuccessRoles->contains('name', $latestRole)) {
            $RoleScore = 1;
        } else {
            if ($SuccessStepup->contains('name', $latestRole)) {
                $RoleScore = 0.75;
            } else {
                $RoleScore = 0;
            }
        }

        //Skill Score
        if ($SuccessSkills->count() > 0) {
            $SkillScore = 0;
            foreach ($PersonSkills as $pK) {
                foreach ($SuccessSkills as $sK) {
                    if ($sK->name == $pK->skill_name) {
                        $SkillScore++;
                        break;
                    }
                }
            }

            $SkillScore /= $SuccessSkills->count();
        } else {
            $SkillScore = 1;
        }


        //Gender Match
        $Gender_Match = 0;
        if ($SuccessGender->contains('name', $ExternalPerson->gender)) {
            $Gender_Match = 1;
        } else {
            $Gender_Match = 0;
        }

        // Location Match
        $Location_Match = 0;
        if ($SuccessLocation->contains('name', $ExternalPerson->location)) {
            $Location_Match = 1;
        } else {
            $Location_Match = 0;
        }

        //Tenture Match


        $roleai = $ExternalPerson->latest_role;
        $locationai = $ExternalPerson->location;
        $functionai = $ExternalPerson->function;
        $divisionai = $ExternalPerson->division;
        $readiness = $ExternalPerson->readiness;

        $inputArray = [
            'role'     => $roleai,
            'location' => $locationai,
            'function' => $functionai,
            'division' => $divisionai
        ];

        // Create a formatted message for GPT-3
        $message = "Generate a summary about the person with the given inputs it should not be more than 50 words:\n";
        foreach ($inputArray as $key => $value) {
            $message .= "$key: \"$value\"\n";
        }

        // Call GPT-3 to generate the headline
        $response = OpenAI::chat()->create([
            'model' => 'gpt-3.5-turbo',
            'messages' => [['role' => 'system', 'content' => $message]],
        ]);
        // dd($ExternalPerson->readiness);
        $generatedHeadline = $response->choices[0]->message->content;
        $newPipeline = pipeline::create([
            'plan_id'            => $planId,
            'user_id'            => $user->id,
            'people_id'          => $ExternalPerson->id,
            'headline'           => $generatedHeadline,
            'first_name'         => $ExternalPerson->forename,
            'last_name'          => $ExternalPerson->surname,
            'middle_name'        => $ExternalPerson->middle_name,
            'other_name'         => $ExternalPerson->other_name,
            'gender'             => $ExternalPerson->gender,
            'diverse'            => $ExternalPerson->diverse,
            'location'           => $ExternalPerson->location,
            'summary'            => $ExternalPerson->summary,
            'country'            => $ExternalPerson->country,
            'city'               => $ExternalPerson->city,
            'linkedinURL'        => $ExternalPerson->linkedinURL,
            'latest_role'        => $ExternalPerson->latest_role,
            'company_id'         => $ExternalPerson->company_id,
            'company_name'       => $ExternalPerson->company_name,
            'start_date'         => $ExternalPerson->start_date,
            'end_date'           => $ExternalPerson->end_date,
            'tenure'             => $ExternalPerson->tenure,
            'function'           => $ExternalPerson->function,
            'division'           => $ExternalPerson->division,
            'seniority'          => $ExternalPerson->seniority,
            'exco'               => $ExternalPerson->exco,
            'career_history'     => $ExternalPerson->career_history,
            'educational_history' => $ExternalPerson->educational_history,
            'skills'             => $ExternalPerson->skills,
            'languages'          => $ExternalPerson->languages,
            'readiness'          => $ExternalPerson->readiness,
            'other_tags'         => $ExternalPerson->other_tags,
            'skills_match'       => $SkillScore,
            'education_match'    => 0,
            'location_match'     => $Location_Match,
            'role_match'         => $RoleScore,
            'gender_match'       => $Gender_Match,
            'tenure_match'       => $tenureMatch,
            'total_score'        => $SkillScore + 0 + $Location_Match + $RoleScore + $Gender_Match + 1,
            'people_type'        => "External-User",
        ]);

        $successPeopleData = [
            'pipeline_id'        => $newPipeline->id,
            'plan_id'            => $planId,
            'user_id'            => $user->id,
            'people_id'          => $ExternalPerson->id,
            'headline'           => $generatedHeadline,
            'first_name'         => $ExternalPerson->forename,
            'last_name'          => $ExternalPerson->surname,
            'middle_name'        => $ExternalPerson->middle_name,
            'other_name'         => $ExternalPerson->other_name,
            'gender'             => $ExternalPerson->gender,
            'diverse'            => $ExternalPerson->diverse,
            'location'           => $ExternalPerson->country,
            'summary'            => $ExternalPerson->summary,
            'linkedinURL'        => $ExternalPerson->linkedinURL,
            'latest_role'        => $ExternalPerson->latest_role,
            'company_id'         => $ExternalPerson->company_id,
            'company_name'       => $ExternalPerson->company_name,
            'start_date'         => $ExternalPerson->start_date,
            'end_date'           => $ExternalPerson->end_date,
            'tenure'             => $ExternalPerson->tenure,
            'function'           => $ExternalPerson->function,
            'division'           => $ExternalPerson->division,
            'seniority'          => $ExternalPerson->seniority,
            'exco'               => $ExternalPerson->exco,
            'career_history'     => $ExternalPerson->career_history,
            'educational_history' => $ExternalPerson->educational_history,
            'skills'             => $ExternalPerson->skills,
            'languages'          => $ExternalPerson->languages,
            'readiness'          => $ExternalPerson->readiness,
            'other_tags'         => $ExternalPerson->other_tags,
            'city'               => $ExternalPerson->city,
            'country'            => $ExternalPerson->country,
            'skills_match'       => $SkillScore,
            'education_match'    => 0,
            'location_match'     => $Location_Match,
            'role_match'         => $RoleScore,
            'gender_match'       => $Gender_Match,
            'tenure_match'       => $tenureMatch,
            'total_score'        => $SkillScore + 0 + $Location_Match + $RoleScore + $Gender_Match + 1,
            'type'               => "External",
            'notes'              => "Enter notes here",
            'status'             => "Approved",
            'recruit'            => 1,
        ];

        if($successionPlan->user_id != auth()->user()->id) {
            $successPeopleData['status'] = "Proposed";
        }

        $SuccessPeople = SuccessPeople::create($successPeopleData);

        
        //------------------- Get the new Gender Diversity Score -----------------//
        $femaleRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(gender = "Female")/Count(*))*100 as female_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $planId)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $planId, 'metric_name' => 'Female-ratio'],
            ['score' => $femaleRatio->female_ratio]
        );

        $maleRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(gender = "Male")/Count(*))*100 as male_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $planId)
            ->first();


        PlanScores::updateOrInsert(
            ['succession_plan_id' => $planId, 'metric_name' => 'Male-Ratio'],
            ['score' => $maleRatio->male_ratio]
        );

        $InternalRatio = DB::table('success_people')
            ->select('plan_id', DB::raw('(SUM(type = "Internal")/Count(*))*100 as internal_ratio'))
            ->groupBy('plan_id')
            ->where('plan_id', $planId)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $planId, 'metric_name' => 'Internal-External Ratio'],
            ['score' => $InternalRatio->internal_ratio]
        );

        $averageskillscore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(skills_match)*100 as average_skill_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $planId)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $planId, 'metric_name' => 'Skill Score'],
            ['score' => $averageskillscore->average_skill_score]
        );

        $averagetenurescore = DB::table('success_people')
            ->select('plan_id', DB::raw('AVG(tenure_match)*100 as average_tenure_score'))
            ->groupBy('plan_id')
            ->where('plan_id', $planId)
            ->first();

        PlanScores::updateOrInsert(
            ['succession_plan_id' => $planId, 'metric_name' => 'Tenure Score'],
            ['score' => $averagetenurescore->average_tenure_score]
        );

        if ($SuccessSkills->isNotEmpty()) {
            // Add requirements for chart for skills radar
            $SkillFound = false;
            foreach ($SuccessSkills as $sK) {
                $SkillFound = false;
                foreach ($PersonSkills as $pK) {
                    if ($sK->name == $pK->skill_name) {
                        SuccessSkills::create([
                            'succession_plan_id' => $planId,
                            'skill_name'        => $sK->name,
                            'success_people_id' => $SuccessPeople->id,
                            'success_requirements_id' => $sK->id,
                            'score' => 1
                        ]);
                        $SkillFound = true;
                        break;
                    }
                }

                if (!$SkillFound) {
                    SuccessSkills::create([
                        'succession_plan_id' => $planId,
                        'success_people_id' => $SuccessPeople->id,
                        'success_requirements_id' => $sK->id,
                        'skill_name'        => $sK->name,
                        'score' => 0
                    ]);
                }
            }
        }
    }
}

if (!function_exists('logQuery')) {

    function logQuery($query, $channel = 'daily')
    {
        if (method_exists($query, 'toSql')) {
            $sql = $query->toSql();
            $bindings = $query->getBindings();

            // Replace placeholders with actual values
            $fullQuery = vsprintf(
                str_replace('?', "'%s'", $sql),
                array_map(fn($binding) => is_string($binding) ? addslashes($binding) : $binding, $bindings)
            );

            // Log the full query
            Log::info('Executed Query: ' . $fullQuery);
        } else {
            Log::warning('Invalid Query Builder provided.');
        }
    }
}
if (!function_exists('logCurrentDateTime')) {
    function logCurrentDateTime($message)
    {
        $time = now()->toDateTimeString();
        Log::info($message . $time);
    }
}

if (!function_exists('cached')) {
    function cached($uriPath)
    {

        $filePath = public_path() . "/" . $uriPath;

        if ($uriPath && file_exists($filePath)) {
            $modifiedTimestamp = filemtime($filePath);
            return "{$uriPath}?{$modifiedTimestamp}";
        }

        return $uriPath;
    }
}

if (!function_exists('renderSuccessPeopleTree')) {
    function renderSuccessPeopleTree($people, $recruitmentProjectId, $plan_user_id)
    {
        $user = auth()->user();
        if (empty($people)) {
            return '';
        }
    
        $html = '<ul>';
        $html .= '<li>';
    
        foreach ($people as $index => $person) {
            // if ($people[$index]->type == "Internal") {
            //     echo "**********<br>count people:  ".count($people). "<br>****<br>";
            //     echo "**********<br>index:  ". $index . "<br>****<br>";
            // }
            
            if($person->type == "Internal") {       
                $addClass = "-red";
            } else {
                $addClass = "";
            }
            $html .= '<a href="#">';
            $html .= '<div wire:key="user-' . (isset($person) ? $person->id : '') . '" class="w-56 HRInfo'.$addClass.' rounded-lg bg-white relative marginClass">
                        <div class="flex gap-10 items-center borderTop'.$addClass.' pb-4">';

                            $html .= '<div class="flex gap-2 item-center mx-3 text-left">';
                            $html .= '<h2 class="font-semibold text-sm whitespace-nowrap w-32"
                                        title="' . htmlspecialchars($person->latest_role) . '">'
                                    . wordwrap(\Illuminate\Support\Str::limit($person->latest_role, 40, '.'), 25, '<br>') .
                                    '</h2>';
                            $html .= '</div>';
    
                            $html .= '<div class="dropdown-container">
                                <button tabindex="1" id="dropdownDefaultButton2" data-dropdown-toggle="dropdown"
                                    class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                    type="button">';
                                    $html .= '<img class="h-5 w-5" src="' . asset('images/DotsThreeVertival.svg') . '" alt="More Options">
                                </button>';
    
                                // Dropdown menu
                                $html .= '<div id="dropdown2"
                                    class="dropdown-menu2 z-10 absolute bg-white divide-y divide-gray-100 rounded-lg w-44 dark:bg-gray-700">
                                    <div class="py-2 ul text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">';
                                $html .= '<div class="cursor-pointer li" @click="vopen = true" wire:click.prevent="viewSelectedPeople(' 
                                    . $person->id . ', ' 
                                    . $person->people_id . ', \'' 
                                    . $person->type . '\')">';        
                                $html  .= '<div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                <span class="font-semibold text-sm">View Profile</span>
                                            </div>
                                        </div>';
                                        if ($person->type != 'Internal') {
                                            $html .= '<div class="cursor-pointer li"';
                                        
                                            if (!$recruitmentProjectId) {
                                                $html .= ' @click="ropen=true; $wire.selectedpersonId=\'' . $person->people_id . '\'"';
                                            }
                                        
                                            $html .= ' wire:click.prevent="';
                                        
                                            if ($recruitmentProjectId) {
                                                $html .= 'savePlanPeopleId(' . $person->people_id . ', ' . $recruitmentProjectId . ')';
                                            } else {
                                                $html .= 'viewSelectedPeople(' . $person->id . ', ' . $person->people_id . ', \'' . htmlspecialchars($person->type, ENT_QUOTES) . '\')';
                                            }
                                        
                                            $html .= '">';
                                        
                                            $html .= '<div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                        <span class="font-semibold text-sm">Recruit</span>
                                                      </div>
                                                    </div>';
                                        } 
                                        // View LinkedIn Button
                                        if (
                                            $person->type !== 'Internal' &&
                                            !empty($person->linkedinURL) &&
                                            $person->linkedinURL !== 'NA'
                                        ) {
                                            $html .= '<div class="cursor-pointer li">';
                                            $html .= '<div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" onclick="window.open(\'' . htmlspecialchars($person->linkedinURL, ENT_QUOTES) . '\', \'_blank\')">';
                                            $html .= '<span class="font-semibold text-sm">View LinkedIn</span>';
                                            $html .= '</div>';
                                            $html .= '</div>';
                                        }

                                        // Approve Functionality
                                        if (
                                            isset($user) && isset($plan_user_id) &&
                                            $user->id === $plan_user_id &&
                                            $person->status === 'Proposed'
                                        ) {
                                            $html .= '<div class="cursor-pointer li" @click="confirmApprovePerson('
                                                . $person->id . ', \'' 
                                                . htmlspecialchars($person->first_name . ' ' . $person->last_name, ENT_QUOTES) . '\', \'' 
                                                . asset('images/redTrashIcon.svg') . '\')">';
                                            $html .= '<div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">';
                                            $html .= '<span class="font-semibold text-sm">Approve</span>';
                                            $html .= '</div>';
                                            $html .= '</div>';
                                        }
                                               
                                        
                                        $html .= '<div class="cursor-pointer li" onclick="confirmRemovePerson('
                                                . $person->id . ', \''
                                                . addslashes(htmlentities($person->first_name . ' ' . $person->last_name)) . '\', \''
                                                . addslashes(htmlentities($person->company_name)) . '\')">
                                            <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                <span class="font-semibold textRed text-sm">Delete</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>';
    
                        // Border
                        $html .= '<div class="border"></div>';
    
                        // Name and company
                        $html .= '<div class="p-2 flex flex-col text-left mb-3">
                            <span class="text-sm whitespace-nowrap w-48" title="' . htmlspecialchars($person->first_name . ' ' . $person->last_name) . '">
                                ' . htmlspecialchars($person->first_name . ' ' . $person->last_name) . '
                            </span>';
                            $html .= '<span class="text-xs text-gray-500">Company: ' 
                            . nl2br(wordwrap(htmlspecialchars($person->company_name), 20, "\n")) . 
                            '</span>';
                            
                        $html .= '</div>';
    
                        // Drag icon
                        $html .= '<img class="absolute dragIcon left-1/2" src="' . asset('images/dragcircle.svg') . '" alt="Drag Icon">
    
                    </div>
                </a>';
    
            // Recursive call for next person
            if ($people->count() > 1) {
                $remaining = $people->slice(1)->values(); // use Collection methods
                $html .= renderSuccessPeopleTree($remaining, $recruitmentProjectId, $plan_user_id);
                break;
            }
        }

        $html .= '</li></ul>';
    
        // $html .= '</li>';
        // $html .= '</ul>';
    
        return $html;
    }     

}

if (!function_exists('renderOrgTree')) {
    function renderOrgTree($node, $isRoot = true, $html = '')
    {
        if (!$node) return '';

        
            $html = '<div class="tree customHeight flex justify-center items-center initial">';
            $html .= '<ul>';
            $html .= '<li>';
        


        // Node content
        $fullName = $node['first_name'] . ' ' . $node['last_name'];
        $role = $node['latest_role'] ?? '';
        $company = $node['company_name'] ?? '';
        

        // Custom HTML block replacing <a>
    $html .= '
    <a href="#">
        <div wire:key="user-' . $node['id'] . '" class="w-56 HRInfo rounded-lg bg-white relative marginClass">
            <div class="flex gap-10 items-center borderTop pb-4">
                <div class="flex gap-2 item-center mx-3 text-left">
                    <h2 class="font-semibold text-sm whitespace-nowrap w-32"
                        title="' . $role . '">
                        ' . wordwrap($role, 25, '<br>') . '
                    </h2>
                </div>
                <div class="dropdown-container">
                    <div id="dropdown1" class="dropdown-menu z-10 absolute bg-white divide-y divide-gray-100 rounded-lg w-44 dark:bg-gray-700">
                        <div class="py-2 ul text-sm text-gray-700 dark:text-gray-200">
                            <div class="cursor-pointer li" @click="rvopen = true" wire:click.prevent="viewSelectedPeople(null, ' . $node['id'] . ', \'' . strtolower($node['type']) . '\')">
                                <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                    <span class="font-semibold text-sm">View</span>
                                </div>
                            </div>
                            <div class="cursor-pointer li" @click="confirmRemovePerson(' . $node['id'] . ', \'' . $fullName . '\', \'' . asset('images/redTrashIcon.svg') . '\')">
                                <div class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                    <span class="font-semibold textRed text-sm">Delete</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="border"></div>
            <div class="p-2 flex flex-col text-left mb-3">
                <span class="text-sm whitespace-nowrap w-48" title="' . $fullName . '">' . $fullName . '</span>
                <span class="text-xs text-gray-500">Company: ' . wordwrap($company, 30, '<br>') . '</span>
            </div>
            <img class="absolute dragIcon left-1/2" src="' . asset('images/dragcircle.svg') . '" alt="Drag Icon">
        </div>
    </a>';

        // Only root can have split children (External & Internal)
        if (!empty($node['children'])) {
            if ($isRoot) {
                $external = array_filter($node['children'], fn($c) => $c['type'] === 'External');
                $internal = array_filter($node['children'], fn($c) => $c['type'] === 'Internal');

                $html .= '<div class="tree customHeight flex justify-center items-center isroot">';
                $html .= '<ul>';

                // Left side External
                $html .= '<li>';
                foreach ($external as $child) {
                    $html .= renderOrgTree($child, false, $html);
                }
                $html .= '</li>';

                // Right side Internal
                $html .= '<li>';
                foreach ($internal as $child) {
                    $html .= renderOrgTree($child, false, $html);
                }
                $html .= '</li>';

                $html .= '</ul>';
                $html .= '</div>'; // Close the tree div
            } else {
                // For non-root: show all children as a chain
                $html .= '<div class="tree customHeight flex justify-center items-center non-root">';
                $html .= '<ul>';
                $html .= '<li>';
                foreach ($node['children'] as $child) {
                    $html .= renderOrgTree($child, false, $html);
                }
                $html .= '</li>';
                $html .= '</ul>';
                $html .= '</div>'; // Close the tree div
            }
        }

        $html .= '</li>';
        $html .= '</ul>';
        $html .= '</div>'; // Close the tree div
        // if ($isRoot) {
        //     $html .= '</div>'; // Close the root div
        // }

        return $html;
    }
}

/**
 * Extract plan data from database for external search
 */
if (!function_exists('extractPlanDataForExternalSearch')) {
    function extractPlanDataForExternalSearch($planId) {
        try {
            // Get the succession plan
            $plan = SuccessionPlan::findOrFail($planId);

            // Get all success requirements for this plan
            $requirements = SuccessRequirements::where('plan_id', $planId)->get();

            // Group requirements by type
            $groupedRequirements = $requirements->groupBy('type');

            // Build the plan data array
            $planData = [
                'plan_id' => $plan->id,
                'plan_name' => $plan->name,
                'description' => $plan->description ?? '',
                'minimum_tenure' => $plan->minimum_Experience ?? 0,
                'target_roles' => $groupedRequirements->get('Role', collect())->pluck('name')->toArray(),
                'step_up_candidates' => $groupedRequirements->get('step_up', collect())->pluck('name')->toArray(),
                'companies' => $groupedRequirements->get('Company', collect())->pluck('name')->toArray(),
                'country' => $groupedRequirements->get('Location', collect())->pluck('name')->toArray(),
                'skills' => $groupedRequirements->get('professional_skill', collect())->pluck('name')->toArray(),
                'qualifications' => $groupedRequirements->get('qualification', collect())->pluck('name')->toArray(),
                'gender' => $groupedRequirements->get('Gender', collect())->first()->name ?? 'Not required',
                'include_alumni' => $groupedRequirements->get('include_alumni', collect())->first()->name === 'Yes' ? true : false,
                'is_ethnicity_important' => $plan->ethnicity ?? false,
            ];

            // Set defaults for empty arrays
            if (empty($planData['target_roles'])) {
                $planData['target_roles'] = [];
            }
            if (empty($planData['companies'])) {
                $planData['companies'] = ['none'];
            }
            if (empty($planData['step_up_candidates'])) {
                $planData['step_up_candidates'] = ['none'];
            }
            if (empty($planData['country'])) {
                $planData['country'] = [];
            }

            Log::info("PLAN DATA EXTRACTION: Successfully extracted plan data for external search", [
                'plan_id' => $planId,
                'plan_name' => $planData['plan_name'],
                'include_alumni' => $planData['include_alumni'],
                'companies_count' => count($planData['companies']),
                'roles_count' => count($planData['target_roles'])
            ]);

            return $planData;

        } catch (\Exception $e) {
            Log::error("PLAN DATA EXTRACTION: Failed to extract plan data for external search", [
                'plan_id' => $planId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }
}

